#!/usr/bin/env node

/**
 * 轮播图按钮内联样式检查工具
 */

const fs = require('fs');
const path = require('path');

function checkCarouselButtonsInline() {
  console.log('🔍 检查轮播图按钮内联样式一致性...\n');
  
  // 检查 HeroSlide.js
  const heroSlidePath = path.join(process.cwd(), 'components/HeroSlide.js');
  const carouselPath = path.join(process.cwd(), 'components/Carousel.js');
  
  if (!fs.existsSync(heroSlidePath)) {
    console.log('❌ 找不到 HeroSlide.js 文件');
    return;
  }
  
  if (!fs.existsSync(carouselPath)) {
    console.log('❌ 找不到 Carousel.js 文件');
    return;
  }
  
  const heroSlideContent = fs.readFileSync(heroSlidePath, 'utf8');
  const carouselContent = fs.readFileSync(carouselPath, 'utf8');
  
  console.log('📋 轮播图按钮内联样式分析：\n');
  
  // 检查内联样式
  const styleMatch = heroSlideContent.match(/style=\{\{([^}]+)\}\}/s);
  if (styleMatch) {
    const styleContent = styleMatch[1];
    console.log('🎨 当前按钮内联样式：');
    console.log(`${styleContent}\n`);
    
    // 分析关键样式属性
    const styleAnalysis = {
      '高度': styleContent.match(/height:\s*'([^']+)'/),
      '最小宽度': styleContent.match(/minWidth:\s*'([^']+)'/),
      '内边距': styleContent.match(/padding:\s*'([^']+)'/),
      '边框半径': styleContent.match(/borderRadius:\s*'([^']+)'/),
      '字体大小': styleContent.match(/fontSize:\s*'([^']+)'/),
      '字体粗细': styleContent.match(/fontWeight:\s*'([^']+)'/),
      '盒模型': styleContent.match(/boxSizing:\s*'([^']+)'/),
    };
    
    console.log('📊 关键样式属性分析：');
    Object.entries(styleAnalysis).forEach(([property, match]) => {
      if (match) {
        console.log(`   ${property}: ${match[1]}`);
      } else {
        console.log(`   ${property}: 未设置`);
      }
    });
    
    // 检查主题样式函数
    const themeStylesMatch = heroSlideContent.match(/getButtonStyles\(([^)]+)\)/);
    if (themeStylesMatch) {
      console.log(`\n🎨 主题样式函数调用: getButtonStyles(${themeStylesMatch[1]})`);
    }
    
    // 检查是否使用了展开运算符
    if (styleContent.includes('...getButtonStyles')) {
      console.log('   ✅ 使用主题样式展开运算符');
    } else {
      console.log('   ⚠️  未使用主题样式展开运算符');
    }
    
  } else {
    console.log('❌ 未找到内联样式定义');
  }
  
  // 检查轮播图数据中的按钮文本
  console.log('\n📝 按钮文本分析：');
  const buttonTextMatches = carouselContent.match(/buttonText:\s*'([^']+)'/g) || [];
  const buttonTexts = buttonTextMatches.map(match => match.match(/'([^']+)'/)[1]);
  
  if (buttonTexts.length > 0) {
    console.log('   按钮文本列表：');
    buttonTexts.forEach((text, index) => {
      console.log(`   ${index + 1}. "${text}" (${text.length} 字符)`);
    });
    
    // 检查文本长度一致性
    const lengths = buttonTexts.map(text => text.length);
    const maxLength = Math.max(...lengths);
    const minLength = Math.min(...lengths);
    
    if (maxLength - minLength <= 2) {
      console.log('   ✅ 按钮文本长度相对一致');
    } else {
      console.log(`   ⚠️  按钮文本长度差异较大 (${minLength}-${maxLength} 字符)`);
    }
  }
  
  // 检查主题配置
  console.log('\n🎨 主题配置分析：');
  const themeMatches = carouselContent.match(/buttonThemeClass:\s*'([^']+)'/g) || [];
  const themes = themeMatches.map(match => match.match(/'([^']+)'/)[1]);
  
  if (themes.length > 0) {
    console.log('   主题列表：');
    themes.forEach((theme, index) => {
      console.log(`   ${index + 1}. ${theme}`);
    });
    
    const uniqueThemes = [...new Set(themes)];
    console.log(`   ✅ 共使用 ${uniqueThemes.length} 种不同主题: ${uniqueThemes.join(', ')}`);
  }
  
  console.log('\n🎉 轮播图按钮内联样式检查完成！');
  console.log('\n💡 新的内联样式方案特点：');
  console.log('   • 固定高度: 56px');
  console.log('   • 最小宽度: 200px');
  console.log('   • 固定内边距: 0 48px');
  console.log('   • 完全圆角: 28px');
  console.log('   • 强制盒模型: border-box');
  console.log('   • 主题色彩: 通过内联样式应用');
}

if (require.main === module) {
  checkCarouselButtonsInline();
}

module.exports = { checkCarouselButtonsInline };
