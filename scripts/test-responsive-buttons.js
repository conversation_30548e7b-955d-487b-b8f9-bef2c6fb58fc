#!/usr/bin/env node

/**
 * 测试轮播图按钮响应式样式
 */

// 复制 HeroSlide.js 中的响应式按钮样式函数
const getResponsiveButtonStyles = (screenWidth) => {
  // 移动端 (< 640px)
  if (screenWidth < 640) {
    return {
      height: '44px',
      minWidth: '140px',
      padding: '0 24px',
      fontSize: '14px',
      borderRadius: '22px',
    };
  }
  
  // 平板端 (640px - 768px)
  if (screenWidth < 768) {
    return {
      height: '48px',
      minWidth: '160px',
      padding: '0 32px',
      fontSize: '15px',
      borderRadius: '24px',
    };
  }
  
  // 桌面端 (≥ 768px)
  return {
    height: '56px',
    minWidth: '200px',
    padding: '0 48px',
    fontSize: '16px',
    borderRadius: '28px',
  };
};

// 主题样式函数
const getButtonStyles = (themeClass) => {
  const themes = {
    buttonThemeDefault: {
      backgroundColor: '#1f2937',
      color: '#ffffff',
    },
    buttonThemeBlack: {
      backgroundColor: '#000000',
      color: '#ffffff',
    },
    buttonThemeWhite: {
      backgroundColor: '#ffffff',
      color: '#000000',
    },
    buttonThemeBlue: {
      backgroundColor: '#2563eb',
      color: '#ffffff',
    },
  };
  
  return themes[themeClass] || themes.buttonThemeDefault;
};

// 基础按钮样式
const baseButtonStyle = {
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontWeight: '700',
  textDecoration: 'none',
  border: 'none',
  outline: 'none',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  boxSizing: 'border-box',
  whiteSpace: 'nowrap',
};

function testResponsiveButtons() {
  console.log('📱 测试轮播图按钮响应式样式\n');
  
  const testScreenSizes = [
    { name: '小手机', width: 375 },
    { name: '大手机', width: 414 },
    { name: '小平板', width: 640 },
    { name: '大平板', width: 768 },
    { name: '小桌面', width: 1024 },
    { name: '大桌面', width: 1440 },
  ];
  
  const themes = ['buttonThemeBlack', 'buttonThemeWhite', 'buttonThemeBlue'];
  const buttonTexts = ['了解更多', '立即购买'];
  
  testScreenSizes.forEach((screen, index) => {
    console.log(`📐 ${screen.name} (${screen.width}px)`);
    
    const responsiveStyles = getResponsiveButtonStyles(screen.width);
    
    // 确定设备类型
    let deviceType = '桌面端';
    if (screen.width < 640) deviceType = '移动端';
    else if (screen.width < 768) deviceType = '平板端';
    
    console.log(`   设备类型: ${deviceType}`);
    console.log('   按钮尺寸:');
    console.log(`   • 高度: ${responsiveStyles.height}`);
    console.log(`   • 最小宽度: ${responsiveStyles.minWidth}`);
    console.log(`   • 内边距: ${responsiveStyles.padding}`);
    console.log(`   • 字体大小: ${responsiveStyles.fontSize}`);
    console.log(`   • 边框半径: ${responsiveStyles.borderRadius}`);
    
    // 计算按钮实际宽度
    buttonTexts.forEach(text => {
      const fontSize = parseInt(responsiveStyles.fontSize);
      const estimatedTextWidth = text.length * fontSize;
      const paddingWidth = parseInt(responsiveStyles.padding.split(' ')[1]) * 2;
      const minWidth = parseInt(responsiveStyles.minWidth);
      const estimatedTotalWidth = Math.max(minWidth, estimatedTextWidth + paddingWidth);
      
      console.log(`   • "${text}" 预估宽度: ${estimatedTotalWidth}px`);
    });
    
    console.log('');
  });
  
  console.log('✅ 响应式测试完成！');
  console.log('\n📊 响应式断点总结：');
  console.log('   📱 移动端 (< 640px): 44px高 × 140px宽, 14px字体');
  console.log('   📟 平板端 (640-768px): 48px高 × 160px宽, 15px字体');
  console.log('   💻 桌面端 (≥ 768px): 56px高 × 200px宽, 16px字体');
  
  console.log('\n🎯 移动端优化特点：');
  console.log('   • 按钮高度减少到 44px (符合移动端触摸标准)');
  console.log('   • 按钮宽度减少到 140px (适应小屏幕)');
  console.log('   • 内边距减少到 24px (节省空间)');
  console.log('   • 字体大小减少到 14px (保持可读性)');
  console.log('   • 边框半径按比例调整 (保持视觉一致性)');
  
  console.log('\n🔄 自动适配机制：');
  console.log('   • 实时检测屏幕尺寸变化');
  console.log('   • 自动应用对应断点样式');
  console.log('   • 保持所有按钮在同一设备上的一致性');
  console.log('   • 支持设备旋转和窗口缩放');
}

if (require.main === module) {
  testResponsiveButtons();
}

module.exports = { testResponsiveButtons, getResponsiveButtonStyles };
