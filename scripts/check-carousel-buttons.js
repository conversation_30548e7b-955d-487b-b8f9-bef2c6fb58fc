#!/usr/bin/env node

/**
 * 轮播图按钮样式一致性检查工具
 */

const fs = require('fs');
const path = require('path');

function checkCarouselButtons() {
  console.log('🔍 检查轮播图按钮样式一致性...\n');
  
  // 检查 HeroSlide.js
  const heroSlidePath = path.join(process.cwd(), 'components/HeroSlide.js');
  const carouselPath = path.join(process.cwd(), 'components/Carousel.js');
  
  if (!fs.existsSync(heroSlidePath)) {
    console.log('❌ 找不到 HeroSlide.js 文件');
    return;
  }
  
  if (!fs.existsSync(carouselPath)) {
    console.log('❌ 找不到 Carousel.js 文件');
    return;
  }
  
  const heroSlideContent = fs.readFileSync(heroSlidePath, 'utf8');
  const carouselContent = fs.readFileSync(carouselPath, 'utf8');
  
  console.log('📋 轮播图按钮样式分析：\n');
  
  // 检查按钮样式类
  const buttonClassMatch = heroSlideContent.match(/className=\{`([^}]+)\$\{currentButtonTheme\}`\}/);
  if (buttonClassMatch) {
    const buttonClasses = buttonClassMatch[1];
    console.log('🎨 当前按钮样式类：');
    console.log(`   ${buttonClasses}\n`);
    
    // 分析样式组件
    const styleComponents = {
      '内边距 (垂直)': buttonClasses.match(/py-\d+/g) || [],
      '内边距 (水平)': buttonClasses.match(/px-\d+/g) || [],
      '文字大小': buttonClasses.match(/text-\w+/g) || [],
      '最小尺寸': buttonClasses.match(/min-[hw]-\[[^\]]+\]/g) || [],
      '响应式断点': buttonClasses.match(/(?:sm|md|lg|xl):[^\\s]+/g) || [],
    };
    
    console.log('📊 样式组件分析：');
    Object.entries(styleComponents).forEach(([category, values]) => {
      console.log(`   ${category}: ${values.length > 0 ? values.join(', ') : '无'}`);
    });
    
    // 检查问题
    console.log('\n🔍 潜在问题检查：');
    
    // 检查重复断点
    const duplicateBreakpoints = [];
    const breakpoints = buttonClasses.match(/(?:sm|md|lg|xl):[^\\s]+/g) || [];
    const breakpointPrefixes = breakpoints.map(bp => bp.split(':')[0]);
    const duplicates = breakpointPrefixes.filter((item, index) => breakpointPrefixes.indexOf(item) !== index);
    
    if (duplicates.length > 0) {
      console.log(`   ⚠️  发现重复断点: ${[...new Set(duplicates)].join(', ')}`);
    } else {
      console.log('   ✅ 无重复断点');
    }
    
    // 检查标准间距
    const spacingValues = [...(buttonClasses.match(/p[xy]?-\d+/g) || [])];
    const nonStandardSpacing = spacingValues.filter(spacing => {
      const value = parseInt(spacing.match(/\d+/)[0]);
      return ![2, 4, 6, 8, 10, 12, 16, 20, 24].includes(value);
    });
    
    if (nonStandardSpacing.length > 0) {
      console.log(`   ⚠️  非标准间距: ${nonStandardSpacing.join(', ')}`);
    } else {
      console.log('   ✅ 间距符合标准');
    }
    
    // 检查最小尺寸一致性
    const minHeight = buttonClasses.match(/min-h-\[[^\]]+\]/);
    const minWidth = buttonClasses.match(/min-w-\[[^\]]+\]/);
    
    if (minHeight && minWidth) {
      console.log(`   ✅ 设置了最小尺寸: ${minHeight[0]}, ${minWidth[0]}`);
    } else {
      console.log('   ⚠️  缺少最小尺寸设置');
    }
  }
  
  // 检查轮播图数据中的按钮文本
  console.log('\n📝 按钮文本分析：');
  const buttonTextMatches = carouselContent.match(/buttonText:\s*'([^']+)'/g) || [];
  const buttonTexts = buttonTextMatches.map(match => match.match(/'([^']+)'/)[1]);
  
  if (buttonTexts.length > 0) {
    console.log('   按钮文本列表：');
    buttonTexts.forEach((text, index) => {
      console.log(`   ${index + 1}. "${text}" (${text.length} 字符)`);
    });
    
    // 检查文本长度一致性
    const lengths = buttonTexts.map(text => text.length);
    const maxLength = Math.max(...lengths);
    const minLength = Math.min(...lengths);
    
    if (maxLength - minLength <= 2) {
      console.log('   ✅ 按钮文本长度相对一致');
    } else {
      console.log(`   ⚠️  按钮文本长度差异较大 (${minLength}-${maxLength} 字符)`);
    }
  }
  
  console.log('\n🎉 轮播图按钮样式检查完成！');
}

if (require.main === module) {
  checkCarouselButtons();
}

module.exports = { checkCarouselButtons };
