#!/usr/bin/env node

/**
 * 最终断点清理脚本
 * 处理剩余的复杂断点问题
 */

const fs = require('fs');
const path = require('path');

// 最终修复规则 - 处理复杂情况
const finalFixRules = [
  // 修复复杂的布局断点
  {
    name: '修复复杂 flex 布局',
    pattern: /\bflex flex-col lg:flex-row\b/g,
    replacement: 'flex flex-col sm:flex-col lg:flex-row'
  },
  {
    name: '修复复杂 text-center 布局',
    pattern: /\btext-center lg:text-left\b/g,
    replacement: 'text-center sm:text-center lg:text-left'
  },
  
  // 修复剩余的间距问题
  {
    name: '修复 px-1 为标准间距',
    pattern: /\bpx-1\b/g,
    replacement: 'px-2'
  },
  {
    name: '修复 py-1 为标准间距',
    pattern: /\bpy-1\b/g,
    replacement: 'py-2'
  },
  {
    name: '修复 p-1 为标准间距',
    pattern: /\bp-1\b/g,
    replacement: 'p-2'
  },
  
  // 修复特殊的网格布局
  {
    name: '修复特殊网格布局',
    pattern: /\bgrid-cols-1 md:grid-cols-2 2xl:grid-cols-4\b/g,
    replacement: 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4'
  },
  
  // 修复隐藏/显示类
  {
    name: '修复 hidden md:block',
    pattern: /\bhidden md:block\b/g,
    replacement: 'hidden sm:hidden md:block'
  },
  {
    name: '修复 block md:hidden',
    pattern: /\bblock md:hidden\b/g,
    replacement: 'block sm:block md:hidden'
  },
  
  // 修复宽度类
  {
    name: '修复 w-full md:w-auto',
    pattern: /\bw-full md:w-auto\b/g,
    replacement: 'w-full sm:w-full md:w-auto'
  },
  
  // 修复特殊的文字大小
  {
    name: '修复 text-4xl md:text-5xl',
    pattern: /\btext-4xl md:text-5xl\b/g,
    replacement: 'text-4xl sm:text-4xl md:text-5xl'
  },
  {
    name: '修复 text-3xl lg:text-4xl',
    pattern: /\btext-3xl lg:text-4xl\b/g,
    replacement: 'text-3xl sm:text-3xl md:text-3xl lg:text-4xl'
  },
];

// 特殊文件处理规则
const specialFileRules = {
  'components/BenefitSection.js': [
    {
      pattern: /gap-10 lg:gap-12/g,
      replacement: 'gap-8 sm:gap-10 lg:gap-12'
    }
  ],
  'pages/solutions/index.js': [
    {
      pattern: /text-4xl sm:text-4xl md:text-5xl/g,
      replacement: 'text-3xl sm:text-4xl md:text-5xl'
    }
  ]
};

// 修复单个文件
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const appliedFixes = [];
  
  // 应用通用规则
  finalFixRules.forEach(rule => {
    const originalContent = content;
    content = content.replace(rule.pattern, rule.replacement);
    
    if (content !== originalContent) {
      hasChanges = true;
      appliedFixes.push(rule.name);
    }
  });
  
  // 应用特殊文件规则
  const relativePath = path.relative(process.cwd(), filePath);
  if (specialFileRules[relativePath]) {
    specialFileRules[relativePath].forEach(rule => {
      const originalContent = content;
      content = content.replace(rule.pattern, rule.replacement);
      
      if (content !== originalContent) {
        hasChanges = true;
        appliedFixes.push(`特殊规则: ${relativePath}`);
      }
    });
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 修复文件: ${filePath}`);
    appliedFixes.forEach(fix => {
      console.log(`   - ${fix}`);
    });
  }
  
  return { hasChanges, appliedFixes };
}

// 扫描并修复目录
function fixDirectory(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let totalFixed = 0;
  let totalFiles = 0;
  
  function walkDir(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    files.forEach(file => {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDir(filePath);
      } else if (stat.isFile() && extensions.some(ext => file.endsWith(ext))) {
        totalFiles++;
        const result = fixFile(filePath);
        if (result.hasChanges) {
          totalFixed++;
        }
      }
    });
  }
  
  walkDir(dir);
  return { totalFixed, totalFiles };
}

// 主函数
function main() {
  const projectRoot = process.cwd();
  const dirsToFix = ['components', 'pages'];
  
  console.log('🔧 开始最终断点清理...\n');
  
  let totalFixed = 0;
  let totalFiles = 0;
  
  dirsToFix.forEach(dir => {
    const dirPath = path.join(projectRoot, dir);
    if (fs.existsSync(dirPath)) {
      console.log(`🔍 清理目录: ${dir}`);
      const result = fixDirectory(dirPath);
      totalFixed += result.totalFixed;
      totalFiles += result.totalFiles;
      console.log(`   清理了 ${result.totalFixed}/${result.totalFiles} 个文件\n`);
    }
  });
  
  console.log(`\n📊 清理完成:`);
  console.log(`   总共扫描: ${totalFiles} 个文件`);
  console.log(`   成功清理: ${totalFixed} 个文件`);
  
  if (totalFixed > 0) {
    console.log('\n🎉 建议运行以下命令验证清理效果:');
    console.log('   npm run check-breakpoints');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, fixDirectory };
