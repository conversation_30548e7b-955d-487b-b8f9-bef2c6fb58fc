#!/usr/bin/env node

/**
 * 断点使用检查工具
 * 检查项目中断点使用是否符合标准
 */

const fs = require('fs');
const path = require('path');

// 检查规则
const rules = {
  // 跳跃断点检查
  skipBreakpoints: {
    name: '跳跃断点检查',
    pattern: /\b(text-xs|text-sm|text-base|text-lg|text-xl|text-2xl|text-3xl|text-4xl|text-5xl|text-6xl)\s+(?:lg:|xl:|2xl:)/g,
    message: '发现跳跃断点使用，建议使用渐进式断点'
  },
  
  // 不一致间距检查
  inconsistentSpacing: {
    name: '不一致间距检查',
    pattern: /\b(px-[1357]|py-[1357]|p-[1357])\b/g,
    message: '发现非标准间距值，建议使用 4, 6, 8 的倍数'
  },
  
  // 缺少移动端断点
  missingMobile: {
    name: '缺少移动端断点检查',
    pattern: /\b(md:|lg:|xl:|2xl:)(?!.*(?:sm:|xs:))/g,
    message: '发现直接使用大屏断点，建议添加移动端断点'
  }
};

// 扫描文件
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  Object.entries(rules).forEach(([ruleKey, rule]) => {
    const matches = content.match(rule.pattern);
    if (matches) {
      matches.forEach(match => {
        const lineNumber = content.substring(0, content.indexOf(match)).split('\n').length;
        issues.push({
          file: filePath,
          line: lineNumber,
          rule: rule.name,
          match: match.trim(),
          message: rule.message
        });
      });
    }
  });
  
  return issues;
}

// 扫描目录
function scanDirectory(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let allIssues = [];
  
  function walkDir(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    files.forEach(file => {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDir(filePath);
      } else if (stat.isFile() && extensions.some(ext => file.endsWith(ext))) {
        const issues = scanFile(filePath);
        allIssues = allIssues.concat(issues);
      }
    });
  }
  
  walkDir(dir);
  return allIssues;
}

// 生成报告
function generateReport(issues) {
  if (issues.length === 0) {
    console.log('✅ 未发现断点使用问题！');
    return;
  }
  
  console.log(`\n🔍 发现 ${issues.length} 个断点使用问题：\n`);
  
  // 按文件分组
  const issuesByFile = issues.reduce((acc, issue) => {
    if (!acc[issue.file]) {
      acc[issue.file] = [];
    }
    acc[issue.file].push(issue);
    return acc;
  }, {});
  
  Object.entries(issuesByFile).forEach(([file, fileIssues]) => {
    console.log(`📄 ${file}`);
    fileIssues.forEach(issue => {
      console.log(`  ⚠️  第${issue.line}行: ${issue.message}`);
      console.log(`      匹配内容: "${issue.match}"`);
    });
    console.log('');
  });
  
  // 统计信息
  const ruleStats = issues.reduce((acc, issue) => {
    acc[issue.rule] = (acc[issue.rule] || 0) + 1;
    return acc;
  }, {});
  
  console.log('📊 问题统计：');
  Object.entries(ruleStats).forEach(([rule, count]) => {
    console.log(`  ${rule}: ${count} 个`);
  });
}

// 主函数
function main() {
  const projectRoot = process.cwd();
  const dirsToScan = ['components', 'pages', 'app'];
  
  console.log('🔍 开始检查断点使用规范...\n');
  
  let allIssues = [];
  
  dirsToScan.forEach(dir => {
    const dirPath = path.join(projectRoot, dir);
    if (fs.existsSync(dirPath)) {
      console.log(`扫描目录: ${dir}`);
      const issues = scanDirectory(dirPath);
      allIssues = allIssues.concat(issues);
    }
  });
  
  generateReport(allIssues);
  
  // 退出码
  process.exit(allIssues.length > 0 ? 1 : 0);
}

if (require.main === module) {
  main();
}

module.exports = { scanFile, scanDirectory, generateReport };
