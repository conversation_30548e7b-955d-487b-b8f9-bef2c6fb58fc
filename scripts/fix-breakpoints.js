#!/usr/bin/env node

/**
 * 批量修复断点使用问题的脚本
 */

const fs = require('fs');
const path = require('path');

// 修复规则
const fixRules = [
  // 修复常见的间距问题
  {
    name: '修复 px-5 为标准间距',
    pattern: /\bpx-5\b/g,
    replacement: 'px-4 sm:px-6 lg:px-8'
  },
  {
    name: '修复 py-5 为标准间距',
    pattern: /\bpy-5\b/g,
    replacement: 'py-6'
  },
  {
    name: '修复 p-5 为标准间距',
    pattern: /\bp-5\b/g,
    replacement: 'p-4 sm:p-6'
  },
  {
    name: '修复 py-3 为标准间距',
    pattern: /\bpy-3\b/g,
    replacement: 'py-4'
  },
  
  // 修复常见的跳跃断点
  {
    name: '修复 grid md: 缺少 sm:',
    pattern: /\bgrid grid-cols-1 md:grid-cols-2\b/g,
    replacement: 'grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2'
  },
  {
    name: '修复 grid md: lg: 缺少 sm:',
    pattern: /\bgrid grid-cols-1 md:grid-cols-2 lg:grid-cols-3\b/g,
    replacement: 'grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
  },
  {
    name: '修复 flex md: 缺少 sm:',
    pattern: /\bflex flex-col md:flex-row\b/g,
    replacement: 'flex flex-col sm:flex-col md:flex-row'
  },
  
  // 修复文字大小跳跃
  {
    name: '修复 text-lg md: 缺少 sm:',
    pattern: /\btext-lg md:text-xl\b/g,
    replacement: 'text-lg sm:text-lg md:text-xl'
  },
  {
    name: '修复 text-base md: 缺少 sm:',
    pattern: /\btext-base md:text-lg\b/g,
    replacement: 'text-base sm:text-base md:text-lg'
  },
  {
    name: '修复 text-2xl md: 缺少 sm:',
    pattern: /\btext-2xl md:text-3xl\b/g,
    replacement: 'text-2xl sm:text-2xl md:text-3xl'
  },

  // 修复更多间距问题
  {
    name: '修复 px-3 为标准间距',
    pattern: /\bpx-3\b/g,
    replacement: 'px-4'
  },
  {
    name: '修复 py-7 为标准间距',
    pattern: /\bpy-7\b/g,
    replacement: 'py-8'
  },
  {
    name: '修复 p-3 为标准间距',
    pattern: /\bp-3\b/g,
    replacement: 'p-4'
  },

  // 修复宽度断点
  {
    name: '修复 w-full md: 缺少 sm:',
    pattern: /\bw-full md:w-1\/2\b/g,
    replacement: 'w-full sm:w-full md:w-1/2'
  },
  {
    name: '修复 w-full md: 缺少 sm:',
    pattern: /\bw-full md:w-1\/3\b/g,
    replacement: 'w-full sm:w-full md:w-1/3'
  },
];

// 修复单个文件
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const appliedFixes = [];
  
  fixRules.forEach(rule => {
    const originalContent = content;
    content = content.replace(rule.pattern, rule.replacement);
    
    if (content !== originalContent) {
      hasChanges = true;
      appliedFixes.push(rule.name);
    }
  });
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 修复文件: ${filePath}`);
    appliedFixes.forEach(fix => {
      console.log(`   - ${fix}`);
    });
  }
  
  return { hasChanges, appliedFixes };
}

// 扫描并修复目录
function fixDirectory(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let totalFixed = 0;
  let totalFiles = 0;
  
  function walkDir(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    files.forEach(file => {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDir(filePath);
      } else if (stat.isFile() && extensions.some(ext => file.endsWith(ext))) {
        totalFiles++;
        const result = fixFile(filePath);
        if (result.hasChanges) {
          totalFixed++;
        }
      }
    });
  }
  
  walkDir(dir);
  return { totalFixed, totalFiles };
}

// 主函数
function main() {
  const projectRoot = process.cwd();
  const dirsToFix = ['components', 'pages'];
  
  console.log('🔧 开始批量修复断点使用问题...\n');
  
  let totalFixed = 0;
  let totalFiles = 0;
  
  dirsToFix.forEach(dir => {
    const dirPath = path.join(projectRoot, dir);
    if (fs.existsSync(dirPath)) {
      console.log(`🔍 修复目录: ${dir}`);
      const result = fixDirectory(dirPath);
      totalFixed += result.totalFixed;
      totalFiles += result.totalFiles;
      console.log(`   修复了 ${result.totalFixed}/${result.totalFiles} 个文件\n`);
    }
  });
  
  console.log(`\n📊 修复完成:`);
  console.log(`   总共扫描: ${totalFiles} 个文件`);
  console.log(`   成功修复: ${totalFixed} 个文件`);
  
  if (totalFixed > 0) {
    console.log('\n🎉 建议运行以下命令验证修复效果:');
    console.log('   npm run check-breakpoints');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, fixDirectory };
