#!/usr/bin/env node

/**
 * 测试轮播图按钮Tailwind响应式实现
 */

const fs = require('fs');
const path = require('path');

function testTailwindResponsiveButtons() {
  console.log('🎨 测试轮播图按钮Tailwind响应式实现\n');
  
  // 检查 HeroSlide.js
  const heroSlidePath = path.join(process.cwd(), 'components/HeroSlide.js');
  
  if (!fs.existsSync(heroSlidePath)) {
    console.log('❌ 找不到 HeroSlide.js 文件');
    return;
  }
  
  const heroSlideContent = fs.readFileSync(heroSlidePath, 'utf8');
  
  console.log('📋 Tailwind响应式按钮分析：\n');
  
  // 检查按钮className (查找Link组件的className，支持多行)
  const linkMatch = heroSlideContent.match(/className=\{`([\s\S]*?)`\.replace\(\/\\s\+\/g, ' '\)\.trim\(\)\}/);
  if (linkMatch) {
    const classContent = linkMatch[1];
    console.log('🎨 当前按钮Tailwind类：');    console.log(classContent.replace(/\s+/g, ' ').trim());
    console.log('');
    
    // 分析响应式类
    const responsiveAnalysis = {
      '高度类': classContent.match(/h-\d+|sm:h-\d+|md:h-\d+|lg:h-\d+/g) || [],
      '最小宽度类': classContent.match(/min-w-\[[^\]]+\]|sm:min-w-\[[^\]]+\]|md:min-w-\[[^\]]+\]/g) || [],
      '内边距类': classContent.match(/px-\d+|sm:px-\d+|md:px-\d+|lg:px-\d+/g) || [],
      '文字大小类': classContent.match(/text-\w+|sm:text-\w+|md:text-\w+|lg:text-\w+/g) || [],
      '边框半径类': classContent.match(/rounded-\[[^\]]+\]|sm:rounded-\w+|md:rounded-\[[^\]]+\]/g) || [],
    };
    
    console.log('📊 响应式类分析：');
    Object.entries(responsiveAnalysis).forEach(([category, values]) => {
      console.log(`   ${category}: ${values.length > 0 ? values.join(', ') : '无'}`);
    });
    
    // 检查断点覆盖
    console.log('\n🔍 断点覆盖检查：');
    const breakpoints = ['默认', 'sm:', 'md:', 'lg:', 'xl:'];
    const coverage = {
      '高度': { '默认': false, 'sm:': false, 'md:': false },
      '宽度': { '默认': false, 'sm:': false, 'md:': false },
      '内边距': { '默认': false, 'sm:': false, 'md:': false },
      '文字': { '默认': false, 'sm:': false, 'md:': false },
      '圆角': { '默认': false, 'sm:': false, 'md:': false },
    };
    
    // 检查高度覆盖
    if (classContent.includes('h-11')) coverage['高度']['默认'] = true;
    if (classContent.includes('sm:h-12')) coverage['高度']['sm:'] = true;
    if (classContent.includes('md:h-14')) coverage['高度']['md:'] = true;
    
    // 检查宽度覆盖
    if (classContent.includes('min-w-[140px]')) coverage['宽度']['默认'] = true;
    if (classContent.includes('sm:min-w-[160px]')) coverage['宽度']['sm:'] = true;
    if (classContent.includes('md:min-w-[200px]')) coverage['宽度']['md:'] = true;
    
    // 检查内边距覆盖
    if (classContent.includes('px-6')) coverage['内边距']['默认'] = true;
    if (classContent.includes('sm:px-8')) coverage['内边距']['sm:'] = true;
    if (classContent.includes('md:px-12')) coverage['内边距']['md:'] = true;
    
    // 检查文字覆盖
    if (classContent.includes('text-sm')) coverage['文字']['默认'] = true;
    if (classContent.includes('sm:text-base')) coverage['文字']['sm:'] = true;
    if (classContent.includes('md:text-base')) coverage['文字']['md:'] = true;
    
    // 检查圆角覆盖
    if (classContent.includes('rounded-[22px]')) coverage['圆角']['默认'] = true;
    if (classContent.includes('sm:rounded-3xl')) coverage['圆角']['sm:'] = true;
    if (classContent.includes('md:rounded-[28px]')) coverage['圆角']['md:'] = true;
    
    Object.entries(coverage).forEach(([property, breakpoints]) => {
      const covered = Object.entries(breakpoints).filter(([_, value]) => value).map(([bp]) => bp);
      const missing = Object.entries(breakpoints).filter(([_, value]) => !value).map(([bp]) => bp);
      
      console.log(`   ${property}: ✅ ${covered.join(', ')} ${missing.length > 0 ? `| ❌ 缺失: ${missing.join(', ')}` : ''}`);
    });
    
  } else {
    console.log('❌ 未找到按钮className定义');
  }
  
  // 检查主题系统
  console.log('\n🎨 主题系统分析：');
  const themeMatch = heroSlideContent.match(/const buttonThemes = \{([^}]+)\}/s);
  if (themeMatch) {
    const themeContent = themeMatch[1];
    const themes = themeContent.match(/(\w+):\s*'([^']+)'/g) || [];
    
    console.log('   主题列表：');
    themes.forEach((theme, index) => {
      const [, name, classes] = theme.match(/(\w+):\s*'([^']+)'/) || [];
      console.log(`   ${index + 1}. ${name}: ${classes}`);
    });
    
    console.log(`   ✅ 共定义 ${themes.length} 个主题`);
  }
  
  console.log('\n✅ Tailwind响应式按钮测试完成！');
  console.log('\n📊 新实现的优势：');
  console.log('   🎯 使用项目统一的Tailwind断点系统');
  console.log('   🚀 无需JavaScript逻辑，性能更好');
  console.log('   🔧 与项目其他组件保持一致');
  console.log('   📱 完整的移动端到桌面端响应式覆盖');
  console.log('   🎨 保持主题色彩系统');
  
  console.log('\n📱 响应式断点映射：');
  console.log('   • 移动端 (默认): 44px高 × 140px宽, 24px内边距, 14px字体');
  console.log('   • 小平板 (sm: 640px+): 48px高 × 160px宽, 32px内边距, 16px字体');
  console.log('   • 桌面端 (md: 768px+): 56px高 × 200px宽, 48px内边距, 16px字体');
  
  console.log('\n🔄 与项目标准的一致性：');
  console.log('   • 使用标准Tailwind断点: sm:(640px), md:(768px)');
  console.log('   • 符合移动优先原则');
  console.log('   • 渐进式尺寸增强');
  console.log('   • 无跳跃式断点');
}

if (require.main === module) {
  testTailwindResponsiveButtons();
}

module.exports = { testTailwindResponsiveButtons };
