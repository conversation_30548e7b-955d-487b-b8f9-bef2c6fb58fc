/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',

    // Or if using `src` directory:
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    // 标准化断点定义
    screens: {
      'xs': '475px',   // 大手机 (新增)
      'sm': '640px',   // 小平板
      'md': '768px',   // 平板
      'lg': '1024px',  // 小桌面
      'xl': '1280px',  // 大桌面
      '2xl': '1536px', // 超大桌面
    },
    extend: {
      // 标准化间距系统
      spacing: {
        '18': '4.5rem',   // 72px - 常用的 py-18
        '22': '5.5rem',   // 88px
        '26': '6.5rem',   // 104px
      },
      // 标准化容器最大宽度
      maxWidth: {
        'screen-2xl': '1536px',
        'container-sm': '640px',
        'container-md': '768px',
        'container-lg': '1024px',
        'container-xl': '1280px',
      }
    },
  },
  plugins: [],
};
