import dynamic from 'next/dynamic';
import SEO from '@/components/SEO';
import { generateOrganizationSchema } from '@/utils/structuredData';
// import Image from 'next/image'; // Keep if used elsewhere, remove if not
import Carousel from '@/components/Carousel';
import ProductShowcase from '@/components/ProductShowcase';
import SectionTitle from '@/components/SectionTitle'; // Keep if used by other direct children, or remove if only MediaPartners used it here.
import SupportLinksSection from '@/components/SupportLinksSection';

// 动态导入非关键组件
const SolutionExplorer = dynamic(() => import('@/components/SolutionExplorer'), {
  loading: () => <div className="py-16 bg-gray-100 text-center">加载解决方案中...</div>,
  ssr: true,
});

const MediaCarousel = dynamic(() => import('@/components/MediaCarousel'), {
  loading: () => <div className="py-18 bg-gray-50 text-center">加载媒体内容中...</div>,
  ssr: false,
});

// import MediaPartners from "@/components/MediaPartners"; // 移除导入

export default function HomePage() {
  const organizationSchema = generateOrganizationSchema();

  return (
    <>
      <SEO
        title="智能网络解决方案"
        description="GL.iNet 提供领先的路由器和网络解决方案，专注于物联网网关、企业网络和智能家居连接。探索我们的产品和解决方案。"
        keywords="GL.iNet, 路由器, 物联网网关, 企业网络, 智能家居, 网络解决方案, OpenWrt"
        ogImage="/images/og-homepage.jpg"
        canonicalUrl="https://www.gl-inet.com"
        structuredData={organizationSchema}
      />

      <Carousel />
      <ProductShowcase />
      <SolutionExplorer />
      <SupportLinksSection />
      <MediaCarousel />
      {/* <MediaPartners /> */}
    </>
  );
}
