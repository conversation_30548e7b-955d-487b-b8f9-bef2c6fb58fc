import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import Button from '../../components/Button';
import GenericCarousel from '../../components/GenericCarousel';

const GatewayToCloudPage = () => {
  const [activeTab, setActiveTab] = useState('on-site');
  const [activeSolutionTab, setActiveSolutionTab] = useState('flexibility');

  const featureTabs = {
    'on-site': {
      title: '全系列物联网网关',
      description:
        'GL.iNet为不同的运营环境提供合规灵活的物联网网关。我们的物联网网关由紧凑的硬件设计而成，具有更小的功能消耗。通过预装OpenWrt使其拓展性更强，我们的工业路由器专为极端环境打造，当主互联网不稳定、丢失或者受到干扰的时候，我们可以提供4G LTE 故障转移快速切换到备用网络连接。我们还提供运营商认证的物联网网关（GL-X750V2C4）和模块化选项（ZigBee、蓝牙、GPS）便于物联网部署。我们所有的物联网网关和路由器采用多层安全机制，为您提供更好的网络保护。',
      image: '/images/solutions/gateway-cloud/tab-on-site-connection.png',
    },
    cloud: {
      title: '远程物联网网关管理',
      description:
        'GL.iNet 开发的云平台可以为用户提供从物联网网关到云端的可靠、安全的连接。平台支持商家通过简单的操作就可以大规模的部署物联网设备，并赋予用户高级远程访问权限和企业级安全协议来保护您的网络。具备全面且用户友好的操作面板，用于实时检测网络健康和自动故障排除。用户可以将设备分组以实现简化管理，创建模板以自动执行重复性任务，使用云平台中的站点到站点功能轻松安全地内部网络环境。',
      image: '/images/solutions/gateway-cloud/tab-cloud-connection.png',
    },
  };

  const solutionHighlights = {
    flexibility: {
      tabLabel: '灵活性',
      icon: '/images/solutions/gateway-cloud/icon-flexibility.png',
      title: '支持多种物联网协议和标准',
      description:
        '通过支持大多数物联网设备和备用电源供应的多种网络协议，确保不间断的数据传输。我们的硬件优化可以满足特定环境的需求，同时兼容MQTT、BLE。',
    },
    adoptability: {
      tabLabel: '可采用性',
      icon: '/images/solutions/gateway-cloud/icon-adoptability.png',
      title: '应对急速扩张的物联网市场',
      description:
        '扩展网络基础设施的同时，通过自动化网络维护最大化的减少物理干扰。将网络设备分组到集中式的云管理平台进行批量式的管理，只需要将网络配置设置成范本，轻松套用到其他网关，即可进行大规模的部署和软件更新，使用云监控和云管理方案来减少人为错误的风险，满足快速的网络扩张期间频繁的检查需求。',
    },
    cost: {
      tabLabel: '成本',
      icon: '/images/solutions/gateway-cloud/icon-cost.png',
      title: '经济高效的物联网网关管理工具',
      description:
        'GL.iNet的每一个物联网网络设备都为解决特定问题而设计，在其指定的环境下优化定制化模块，我们的设备在支持高性能物联网网络的同时节省成本。GL.iNet面向中小型企业以最低的使用成本提供最好的"网关-云端"解决方案。',
    },
    complexity: {
      tabLabel: '复杂性',
      icon: '/images/solutions/gateway-cloud/icon-complexity.png',
      title: '便于部署的用户友好监管面板',
      description:
        'GL.iNet的所有网关都运行在OpenWrt操作系统上，这是一个集用户友好和先进的冗余设置管理平台，拥有强大的技术团队支持，确保即使是最紧急的网络项目只需要更少量的技术技能就能维护。我们的软件和固件设计从易于维护、高度可定制出发，保持重要的信息易于访问，为用户引入最强的技术。',
    },
    scalability: {
      tabLabel: '可扩展性',
      icon: '/images/solutions/gateway-cloud/icon-scalability.png',
      title: '跨办公室的无缝通信',
      description:
        '通过使用云平台进行远程办公室之间的无缝通信访问、管理和批量配置网络硬件，基于Web的编辑器连接设备，实施集中化的安全系统和SDWAN（软件定义广域网）为分支机构提供文件共享，或者从远程位置访问本地资源。',
    },
  };

  const applicationScenarios = {
    traditional: {
      tabLabel: '传统行业应用',
      scenarios: [
        {
          title: '工业',
          img: 'case-industrial.jpg',
          desc: '通过RS485等工厂设备连接到互联网，远程监控和远程多条生产线中的各种设备状态',
        },
        {
          title: '智能家居',
          img: 'case-smart-home.jpg',
          desc: '与用户应用程序无缝连接，以实现对用户，室温等其他电器的完全控制',
        },
        {
          title: '医疗健康',
          img: 'case-medical.jpg',
          desc: '将医疗设备的数据传输到云端进行实时的监控，并支持第三方云端医疗数据分析',
        },
      ],
    },
    emerging: {
      tabLabel: '新兴技术应用',
      scenarios: [
        {
          title: '机器化',
          img: 'case-robotics.jpg',
          desc: '高度便捷性的网关可以使用机器人与其他物联网设备连接并不受其他连接的限制',
        },
        {
          title: '零售业',
          img: 'case-retail.jpg',
          desc: '通过TLS/SSL与后端服务器建立连接，确保POS/ATM设备数据传输的安全性',
        },
        {
          title: '物流与运输',
          img: 'case-logistics.jpg',
          desc: '为车辆追踪提供经济高效的网关，并为第三方系统集成提供云API应用',
        },
      ],
    },
  };

  // 准备轮播数据
  const carouselItems = Object.keys(applicationScenarios).map(key => ({
    id: key,
    ...applicationScenarios[key],
  }));

  const platformFeatures = [
    {
      title: '可视化统计',
      description: '实时观测您网络上的所有物联网网关和设备',
      image: '/images/solutions/gateway-cloud/platform-visualization.png',
    },
    {
      title: '远程访问',
      description: '随时随地管理您的物联网网关和设备连接',
      image: '/images/solutions/gateway-cloud/platform-remote-access.jpg',
    },
    {
      title: '集中式管理',
      description: '在群组中管理您的物联网网关，实现快速轻松的部署',
      image:
        '/images/solutions/gateway-cloud/platform-centralized-management.jpg',
    },
    {
      title: '自动化',
      description: '为固件更新等重复任务创建模板并运用到组群中',
      image: '/images/solutions/gateway-cloud/platform-automation.png',
    },
  ];

  return (
    <>
      <Head>
        <title>网关-云端物联网解决方案 - GL.iNet</title>
        <meta
          name="description"
          content="面向中小型企业的'网关-云端'物联网解决方案，提供广泛的开源物联网网关选择和可定制化的云平台。"
        />
      </Head>

      {/* 面包屑导航 */}
      <div className="bg-white text-black py-4 px-4 sm:px-6 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
        <Link href="/" className="text-green-600 hover:underline">
          首页
        </Link>{' '}
        /{' '}
        <Link href="/solutions" className="text-green-600 hover:underline">
          解决方案
        </Link>{' '}
        / 网关-云端物联网解决方案
      </div>

      {/* 区域一：主视觉横幅 (Hero Banner) */}
      <section
        className="relative bg-cover bg-center bg-no-repeat py-12 md:py-20"
        style={{
          backgroundImage:
            "url('/images/solutions/gateway-cloud/hero-background.jpg')",
        }}
      >
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative z-10 mx-auto max-w-4xl px-4 sm:px-6 text-center text-white">
          <h1 className="mb-6 text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold leading-tight">
            面向中小型企业'网关-云端'物联网解决方案
          </h1>
          <Button href="#" variant="white" size="large">
            申请演示
          </Button>
        </div>
      </section>

      {/* 区域二：方案核心价值区 */}
      <section className="bg-white py-8 md:py-12">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          {/* 新增的文字段落 */}
          <div className="mx-auto max-w-4xl mb-8 sm:mb-12">
            <p className="text-sm sm:text-base text-gray-700 leading-relaxed">
              在GL.iNet，我们为每一个物联网项目需求提供广泛的开源物联网网关选择，以及可定制化的物联网云基础管理平台。业务用户可以从全球的任何地点连接和部署他们的物联网网关。
            </p>
          </div>

          {/* 图片 */}
          <div className="my-8 sm:my-12 md:my-16">
            <img
              src="/images/solutions/gateway-cloud/feature-connection.png"
              alt="连接物联网设备到云端"
              className="mx-auto inline-block max-w-full lg:max-w-4xl"
            />
          </div>

          {/* 文字内容 */}
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-4 text-xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl sm:text-3xl md:text-3xl sm:text-3xl md:text-3xl lg:text-4xl font-bold text-gray-900">
              连接物联网设备到云端
            </h2>
            <p className="mb-6 sm:mb-10 text-sm sm:text-base text-gray-600 leading-relaxed">
              业务用户可以从全球的任何地点连接和部署他们的物联网网关。我们的'网关-云端'物联网解决方案可以帮助我们的业务用户降低管理物联网的复杂程度及维护成本。
            </p>
          </div>
        </div>
      </section>

      {/* 区域三：动态内容切换区 */}
      <section className="bg-white py-10 md:py-18">
        <div className="container mx-auto px-4 sm:px-6">
          {/* 切换按钮 */}
          <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-4">
            <Button
              onClick={() => setActiveTab('on-site')}
              variant={activeTab === 'on-site' ? 'tabActive' : 'tab'}
              size="tab"
              className="w-full sm:w-auto"
            >
              可靠的实地连接
            </Button>
            <Button
              onClick={() => setActiveTab('cloud')}
              variant={activeTab === 'cloud' ? 'tabActive' : 'tab'}
              size="tab"
              className="w-full sm:w-auto"
            >
              安全的云连接
            </Button>
          </div>

          {/* 动态内容 */}
          <div className="mt-8 sm:mt-12 grid grid-cols-1 items-center gap-6 md:grid-cols-2 md:gap-8">
            <div className="order-2 md:order-1 md:flex md:justify-end">
              <div className="max-w-full md:max-w-md md:pr-4">
                <h3 className="mb-4 text-xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl font-bold text-black">
                  {featureTabs[activeTab].title}
                </h3>
                <p className="text-sm sm:text-base text-black leading-relaxed">
                  {featureTabs[activeTab].description}
                </p>
              </div>
            </div>
            <div className="order-1 md:order-2">
              <img
                src={featureTabs[activeTab].image}
                alt={featureTabs[activeTab].title}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* 区域四：解决方案亮点 */}
      <section className="bg-gray-50 py-12 md:py-18">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h2 className="mx-auto mb-8 sm:mb-12 max-w-3xl text-xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl sm:text-3xl md:text-3xl sm:text-3xl md:text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
            我们的"网关-云端"物联网解决方案旨在解决用户实际问题。
          </h2>

          {/* Tab buttons */}
          <div className="inline-flex flex-wrap justify-center rounded-full p-2 border border-gray-600 gap-1">
            {Object.keys(solutionHighlights).map(key => (
              <Button
                key={key}
                onClick={() => setActiveSolutionTab(key)}
                variant={activeSolutionTab === key ? 'tabActive' : 'ghost'}
                size="tabSmall"
                className="shadow-sm text-xs sm:text-sm"
              >
                {solutionHighlights[key].tabLabel}
              </Button>
            ))}
          </div>

          {/* Dynamic Content */}
          <div className="mt-8 sm:mt-12 flex flex-col items-center justify-center gap-6 sm:gap-8 text-center md:text-left md:flex-row">
            <div className="flex-shrink-0">
              <img
                src={solutionHighlights[activeSolutionTab].icon}
                alt={solutionHighlights[activeSolutionTab].title}
                className="h-32 w-32 sm:h-40 sm:w-40 md:h-48 md:w-48 lg:h-60 lg:w-60 mx-auto"
              />
            </div>
            <div className="max-w-full md:max-w-lg md:ml-8">
              <h3 className="mb-4 text-lg sm:text-xl md:text-2xl font-bold text-gray-900">
                {solutionHighlights[activeSolutionTab].title}
              </h3>
              <p className="text-sm sm:text-base leading-relaxed text-gray-600">
                {solutionHighlights[activeSolutionTab].description}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 区域五：物联网网关云端管理平台 */}
      <section className="bg-white py-12 sm:py-16 md:py-24">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h2 className="text-xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl sm:text-3xl md:text-3xl sm:text-3xl md:text-3xl lg:text-4xl font-bold text-gray-900">
            物联网网关云端管理平台
          </h2>
          <p className="mt-4 mb-6 sm:mb-8 text-base sm:text-lg text-gray-600">
            轻松、安全、远程管理您的物联网设备
          </p>
          <Button href="#" variant="black" size="xlarge" className="shadow-lg">
            创建账户
          </Button>

          {/* 移动端优化的网格布局 */}
          <div className="mt-8 sm:mt-12 md:mt-16 grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 max-w-6xl mx-auto">
            {platformFeatures.map((feature) => (
              <div
                key={feature.title}
                className="bg-white hover:bg-gray-50 transition-all duration-300 cursor-pointer rounded-lg group overflow-hidden relative border border-gray-200 shadow-sm hover:shadow-md"
              >
                <div className="p-4 sm:p-6">
                  <h3 className="mb-2 text-lg sm:text-xl font-bold text-center">
                    {feature.title}
                  </h3>
                  <p className="mb-4 text-gray-600 text-center text-sm sm:text-base opacity-70 group-hover:opacity-100 transition-opacity duration-300">
                    {feature.description}
                  </p>
                </div>
                <div className="relative overflow-hidden h-40 sm:h-48 md:h-56">
                  <img
                    src={feature.image}
                    alt={feature.title}
                    className="absolute inset-0 w-full h-full object-contain transition-all duration-300 group-hover:opacity-90 group-hover:scale-[0.98] p-2"
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 sm:mt-12">
            <Link
              href="/solutions/goodcloud"
              className="text-sm sm:text-base font-bold text-gray-800 underline underline-offset-4 hover:text-gray-600 transition-colors duration-200"
            >
              了解更多 &gt;
            </Link>
          </div>
        </div>
      </section>

      {/* 区域六：应用场景 */}
      <section className="bg-white py-8 md:py-12">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="mb-8 sm:mb-12 text-center">
            <h2 className="text-xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl sm:text-3xl md:text-3xl sm:text-3xl md:text-3xl lg:text-4xl font-bold text-gray-900">
              在业务环境中实施物联网
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-sm sm:text-base sm:text-base sm:text-base md:text-lg text-gray-600 leading-relaxed">
              云平台允许企业远程控制和监管物联网网关连接状态的同时确保您的设备的安全性。
            </p>
          </div>

          {/* 轮播容器 */}
          <GenericCarousel
            items={carouselItems}
            autoPlay={true}
            autoPlayInterval={5000}
            showIndicators={true}
            showNavigation={false}
            className="mb-8"
          >
            {item => (
              <div className="flex flex-col sm:flex-col md:flex-row justify-center items-start gap-3 sm:gap-4 md:gap-6 px-2 sm:px-4">
                {item.scenarios.map(scenario => (
                  <div
                    key={scenario.title}
                    className="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-[340px] mx-auto"
                  >
                    <div className="h-40 sm:h-48 relative">
                      <Image
                        src={`/images/solutions/gateway-cloud/${scenario.img}`}
                        alt={`${scenario.title}场景`}
                        fill
                        sizes="(max-width: 768px) 100vw, 340px"
                        className="object-cover"
                      />
                    </div>
                    <div className="p-4 sm:p-6 border border-gray-300 rounded-b-lg">
                      <h3 className="text-lg sm:text-xl md:text-2xl font-black text-gray-900 mb-2 sm:mb-3">
                        {scenario.title}
                      </h3>
                      <p className="text-gray-800 text-xs sm:text-sm leading-relaxed">
                        {scenario.desc}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </GenericCarousel>
        </div>
      </section>

      {/* 区域七：立即开始你的物联网项目 */}
      <section
        className="relative bg-cover bg-center bg-no-repeat py-12 md:py-20"
        style={{
          backgroundImage:
            "url('/images/solutions/gateway-cloud/background.jpg')",
        }}
      >
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative z-10 mx-auto max-w-4xl px-4 sm:px-6 text-center text-white">
          <h1 className="mb-6 text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold leading-tight">
            立即开始你的物联网项目
          </h1>
          <Button href="#" variant="white" size="xlarge" className="font-black">
            联系销售
          </Button>
        </div>
      </section>
    </>
  );
};

export default GatewayToCloudPage;
