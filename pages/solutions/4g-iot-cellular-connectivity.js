import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

/**
 * 4G IoT 蜂窝网络连接页面图片资源清单：
 * 1. /public/images/solutions/4g-iot/hero-background.jpg - Hero 背景图，建议尺寸 1920x600px
 * 2. /public/images/solutions/4g-iot/multiple-protocol.png - 多协议功能图，建议尺寸 600x400px
 * 3. /public/images/solutions/4g-iot/cloud-control.png - 云控制功能图，建议尺寸 600x400px
 * 4. /public/images/solutions/4g-iot/white-label-service.jpg - 白标服务图，建议尺寸 800x500px
 *
 * 注意：所有图片应放置在 /public/images/solutions/4g-iot/ 目录下
 */

export default function CellularConnectivityPage() {
  // 按钮切换状态
  const [activeTab, setActiveTab] = useState('protocols');

  // 功能数据
  const features = {
    protocols: {
      title: '多种物联网协议',
      description:
        'GL.iNet的4G IoT网关支持各种通信协议，包括蓝牙和Thread。我们的设备高度灵活，适用于各种场景，包括工业环境、远程位置连接和大规模设备连接。',
      image: '/images/solutions/4g-iot/multiple-protocol.png',
      link: '/products/',
      linkText: '了解更多产品',
    },
    cloud: {
      title: '集中云控制',
      description:
        'GL.iNet的4G IoT网关附带GoodCloud，我们的远程设备管理平台，提供远程监控、批量配置和网络流量分析。集中式平台简化了设备管理，增加了网络扩展的灵活性，并创建了完整的物联网网络监控。',
      image: '/images/solutions/4g-iot/cloud-control.png',
      link: '/solutions/goodcloud/',
      linkText: '了解 GoodCloud',
    },
  };

  // 功能图标映射
  const featureIconMap = {
    '4G LTE': {
      icon: '/images/solutions/4g-iot/4G-LTE.png',
    },
    Watchdog: {
      icon: '/images/solutions/4g-iot/watch-dog.png',
    },
    GoodCloud: {
      icon: '/images/solutions/4g-iot/goodcloud.png',
    },
    PoE: {
      icon: '/images/solutions/4g-iot/PoE.png',
    },
    双闪存: {
      icon: '/images/solutions/4g-iot/double-flash.png',
    },
    电池: {
      icon: '/images/solutions/4g-iot/battery.png',
    },
    旅行友好: {
      icon: '/images/solutions/4g-iot/travel-portable.png',
    },
  };

  // 4G LTE 网关产品数据
  const lteGateways = [
    {
      id: 'x300b',
      name: 'GL-X300B',
      title: '工业 4G 路由器',
      image: '/images/product-x300b.jpg',
      features: ['4G LTE', 'Watchdog', 'GoodCloud'],
      highlight: '蓝牙模块（可选）',
      bestFor: '最适合自动化',
      badge: '基础合规化',
      link: '/products/gl-x300b',
    },
    {
      id: 'x750v2',
      name: 'GL-X750V2',
      title: '商用 4G 路由器',
      image: '/images/product-x750v2.jpg',
      features: ['4G LTE', 'Watchdog', 'GoodCloud'],
      highlight: '更多 CAT4/CAT6 模块可用',
      bestFor: 'AT&T 物联网认证',
      badge: 'AT&T 物联网认证',
      link: '/products/gl-x750',
    },
    {
      id: 'ap1300lte',
      name: 'GL-AP1300LTE',
      title: '企业级无线吸顶 AP',
      image: '/images/product-ap1300lte.jpg',
      features: ['4G LTE', 'Watchdog', 'PoE'],
      highlight: '蓝牙模块（可选）',
      bestFor: '最适合办公室',
      badge: '基础合规公告',
      link: '/products/gl-ap1300',
    },
    {
      id: 'xe300',
      name: 'GL-XE300',
      title: '4G 智能路由器',
      image: '/images/product-xe300.jpg',
      features: ['4G LTE', '双闪存', '电池'],
      highlight: '蓝牙模块（可选）',
      bestFor: '高度可定制',
      badge: '高度可定制',
      link: '/products/gl-xe300',
    },
    {
      id: 'e750',
      name: 'GL-E750',
      title: '便携式4G路由器',
      image: '/images/product-e750.jpg',
      features: ['4G LTE', '电池', '旅行友好'],
      highlight: '内置 7000mAh 电池',
      bestFor: '最适合旅行者',
      badge: '便携旅行',
      link: '/products/gl-e750',
    },
  ];

  return (
    <>
      <Head>
        <title>为企业和工业物联网提供蜂窝网络连接 - GL.iNet</title>
        <meta
          name="description"
          content="提供LTE连接的多协议物联网网关，用于物联网传感器设备"
        />
      </Head>

      <div className="min-h-screen font-sans">
        {/* 面包屑导航 */}
        <div className="bg-white text-black py-4 px-4 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
          <Link href="/" className="text-green-600 hover:underline">
            首页
          </Link>{' '}
          /{' '}
          <Link href="/solutions" className="text-green-600 hover:underline">
            解决方案
          </Link>{' '}
          / 为企业和工业物联网提供蜂窝网络连接
        </div>

        {/* Hero 区域 */}
        <section
          className="bg-cover bg-center text-white py-16 px-8 text-center relative"
          style={{
            backgroundImage:
              "linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('/images/solutions/4g-iot/hero-background.jpg')",
          }}
        >
          <div className="relative z-10 max-w-3xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 leading-tight">
              为企业和工业物联网提供蜂窝网络连接
            </h1>
            <h2 className="text-lg sm:text-lg sm:text-lg md:text-xl font-normal mb-8 opacity-90">
              提供LTE连接的多协议物联网网关，用于物联网传感器设备
            </h2>
            <Link
              href="#contact-us"
              className="inline-block bg-white text-black py-4 px-10 rounded-full font-semibold text-base transition hover:bg-gray-200 hover:shadow-lg"
            >
              联系销售
            </Link>
          </div>
        </section>

        {/* 主要介绍区域 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-8 text-center">
            <h2 className="text-2xl sm:text-2xl sm:text-2xl md:text-3xl font-semibold mb-6 text-gray-800">
              使用GL.iNet云管理的4G LTE网关构建安全的物联网系统
            </h2>
            <p className="text-base sm:text-base sm:text-base md:text-lg leading-relaxed text-gray-600 max-w-3xl mx-auto">
              GL.iNet的4G
              IoT网关支持各种通信协议，包括蓝牙和Thread。我们的设备高度灵活，适用于各种场景，包括工业环境、远程位置连接和大规模设备连接。
            </p>
          </div>
        </section>

        {/* 核心功能区域 */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-8">
            {/* 按钮切换区域 */}
            <div className="flex justify-center mb-12 gap-5 flex-col md:flex-row">
              <button
                className={`py-[14px] px-[35px] text-base font-semibold rounded-full cursor-pointer transition-all duration-300 w-full md:w-[540px] text-center ${activeTab === 'protocols' ? 'bg-cyan-500 text-white' : 'bg-white text-cyan-500 hover:bg-cyan-50'}`}
                onClick={() => setActiveTab('protocols')}
              >
                多种物联网协议
              </button>
              <button
                className={`py-[14px] px-[35px] text-base font-semibold rounded-full cursor-pointer transition-all duration-300 w-full md:w-[540px] text-center ${activeTab === 'cloud' ? 'bg-cyan-500 text-white' : 'bg-white text-cyan-500 hover:bg-cyan-50'}`}
                onClick={() => setActiveTab('cloud')}
              >
                集中云控制
              </button>
            </div>

            {/* 内容显示区域 */}
            <div className="mt-8">
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div className="text-center">
                  <Image
                    src={features[activeTab].image}
                    alt={features[activeTab].title}
                    width={500}
                    height={400}
                    className="max-w-full h-auto"
                  />
                </div>
                <div className="md:p-8 text-left">
                  {/* <h3 className="text-[1.8rem] font-semibold mb-4 text-gray-800">{features[activeTab].title}</h3> */}
                  <p className="text-base leading-relaxed text-black-800 mb-6">
                    {features[activeTab].description}
                  </p>
                  {/* <Link href={features[activeTab].link} className="inline-block text-cyan-500 font-semibold hover:underline">
                    {features[activeTab].linkText} &rarr;
                  </Link> */}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 4G LTE 网关产品区域 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-8">
            <h2 className="text-3xl font-semibold text-center mb-12 text-gray-800">
              4G LTE 网关
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 justify-items-center">
              {lteGateways.map(product => (
                <Link
                  href={product.link}
                  key={product.id}
                  className={`no-underline text-black w-full max-w-[320px] ${product.id === 'e750' ? 'sm:col-span-2 lg:col-span-2 lg:col-start-2' : ''}`}
                >
                  <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden flex flex-col h-full border border-gray-200">
                    <div className="w-full h-[200px] flex items-center justify-center p-4 bg-white">
                      <Image
                        src={product.image}
                        alt={product.name}
                        width={200}
                        height={150}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                    <div className="p-4 sm:p-6 text-center flex flex-col flex-grow">
                      <h3 className="text-xl font-bold mb-1 text-gray-900">
                        {product.name}
                      </h3>
                      <h4 className="text-base text-gray-500 mb-4 flex items-center justify-center h-12">
                        {product.title}
                      </h4>
                      <div className="flex justify-center items-center my-4 h-16">
                        {product.features.map(feature => {
                          const featureData = featureIconMap[feature];
                          return featureData ? (
                            <Image
                              key={feature}
                              src={featureData.icon}
                              alt={feature}
                              width={80}
                              height={80}
                              className="w-[80px] h-[80px] object-contain mx-1"
                            />
                          ) : null;
                        })}
                      </div>
                      <p className="text-sm text-gray-700 mt-auto pt-4">
                        {product.highlight}
                      </p>
                    </div>
                    <div className="bg-cyan-500 text-white py-4 px-4 text-center font-semibold">
                      {product.bestFor}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* 白标服务区域 */}
        <section
          className="relative py-10 text-white bg-cover bg-center"
          style={{
            backgroundImage:
              "linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/images/solutions/4g-iot/white-label-service.jpg')",
          }}
        >
          <div className="relative z-10 max-w-3xl mx-auto px-8 flex flex-col items-center justify-center text-center min-h-[320px]">
            <div>
              <h2 className="text-4xl font-bold mb-6">中小企业白标服务</h2>
              <p className="text-lg leading-relaxed mb-8 max-w-2xl">
                GL.iNet的OEM白标解决方案提供网络硬件和软件，拥有良好的国际市场兴趣和客户反馈，消除客户研究和开发新设备以与其现有网络解决方案集成的需要。
              </p>
              <div className="flex gap-6 flex-col sm:flex-row justify-center">
                <Link
                  href="/solutions/white-label-service/"
                  className="inline-block bg-transparent border-2 border-white text-white py-4 px-10 rounded-full font-semibold text-base text-center transition-colors duration-300 hover:bg-white hover:text-black"
                >
                  了解更多
                </Link>
                <Link
                  href="#contact-us"
                  className="inline-block bg-white text-black py-4 px-10 rounded-full font-semibold text-base text-center transition-colors duration-300 hover:bg-gray-200"
                >
                  联系销售
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* 联系销售区域 */}
        <section id="contact-us" className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-8">
            <h2 className="text-3xl font-bold text-center mb-4 text-gray-800">
              联系我们的销售团队
            </h2>
            <p className="text-lg text-center text-gray-600 max-w-3xl mx-auto mb-12">
              您不仅是我们的客户，也是我们的合作伙伴，我们随时为您提供帮助！
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="bg-gray-50 rounded-lg overflow-hidden">
                <h3 className="text-xl font-semibold mb-2 text-gray-800">
                  销售咨询
                </h3>
                <p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-cyan-600 hover:underline underline-offset-4 break-all"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
              <div className="bg-gray-50 rounded-lg overflow-hidden">
                <h3 className="text-xl font-semibold mb-2 text-gray-800">
                  技术支持
                </h3>
                <p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-cyan-600 hover:underline underline-offset-4 break-all"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
              <div className="bg-gray-50 rounded-lg overflow-hidden">
                <h3 className="text-xl font-semibold mb-2 text-gray-800">
                  销售热线
                </h3>
                <p>
                  <a
                    href="tel:************"
                    className="text-cyan-600 hover:underline underline-offset-4"
                  >
                    ************
                  </a>
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
