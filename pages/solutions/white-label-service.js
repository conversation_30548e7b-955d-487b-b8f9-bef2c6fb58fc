import Head from 'next/head';
import Link from 'next/link';
import WhiteLabelHero from '@/components/solutions/WhiteLabelHero';
import HowItWorks from '@/components/solutions/HowItWorks';
import StartProjectCTA from '@/components/solutions/StartProjectCTA';
import CustomizationOptions from '@/components/solutions/CustomizationOptions';
import BusinessPlatform from '@/components/solutions/BusinessPlatform';
import ProjectScales from '@/components/solutions/ProjectScales';
import WhyPartner from '@/components/solutions/WhyPartner';
import MediaPartners from '@/components/MediaPartners';
import ContactSection from '@/components/solutions/ContactSection';

export default function WhiteLabelServicePage() {
  return (
    <>
      <Head>
        <title>白标及客制化服务 - GL.iNet</title>
        <meta
          name="description"
          content="GL.iNet 提供专业的白标和客制化服务，满足您独特的业务需求。"
        />
      </Head>

      <div className="bg-white text-black py-4 px-4 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
        <Link href="/" className="text-green-600 hover:underline">
          首页
        </Link>
        <span className="mx-2">/</span>
        <Link href="/solutions" className="text-green-600 hover:underline">
          解决方案
        </Link>
        <span className="mx-2">/</span>
        <span>白标及客制化服务</span>
      </div>

      <div className="w-full overflow-hidden">
        <WhiteLabelHero imageUrl="/images/solutions/white-label/hero-background.jpg" />

        <section className="py-16 bg-gray-50">
          <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <p className="text-center text-lg text-gray-700 leading-relaxed">
              GL.iNet
              开发行业先进的硬件和软件网络解决方案，优先考虑安全性和可靠性。我们的合作通过扩展您的服务组合并扩大您的解决方案覆盖范围，将附加价值集成到您的业务模型中。我们在设计和制造多功能路由器和物联网网关以及提供定制软件解决方案方面的成功经验将与您的服务组合无缝集成。
            </p>
          </div>
        </section>

        <HowItWorks />

        <StartProjectCTA imageUrl="/images/solutions/white-label/cta-background.jpg" />

        <CustomizationOptions />
        <BusinessPlatform />
        <ProjectScales />
        <WhyPartner />
        <MediaPartners title="合作伙伴" />
        <ContactSection />
      </div>
    </>
  );
}
