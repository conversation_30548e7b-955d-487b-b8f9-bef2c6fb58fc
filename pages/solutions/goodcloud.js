import Link from 'next/link';
import SEO from '@/components/SEO';
import { generateArticleSchema, generateBreadcrumbSchema } from '@/utils/structuredData';
import GoodCloudHero from '@/components/GoodCloudHero'; // 引入新组件
import GoodCloudFeatureGrid from '@/components/GoodCloudFeatureGrid'; // 引入新组件
import BenefitSection from '@/components/BenefitSection'; // 引入新组件

export default function GoodCloudPage() {
  // 生成结构化数据
  const articleSchema = generateArticleSchema({
    title: 'GoodCloud物联网设备管理系统',
    description: '物联网项目的设备管理、数据收集、处理和可视化',
    imageUrl: 'https://www.gl-inet.com/images/goodcloud/hero-background.jpg',
    publishDate: '2024-01-01',
  });

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: '首页', url: 'https://www.gl-inet.com' },
    { name: '解决方案', url: 'https://www.gl-inet.com/solutions' },
    { name: 'GoodCloud 云平台', url: 'https://www.gl-inet.com/solutions/goodcloud' },
  ]);

  // 示例功能数据，实际数据和图标应由用户提供，或从CMS/API获取
  const goodCloudFeatures = [
    {
      id: 'gc1',
      title: '集中管理',
      description:
        'GoodCloud 是一个基于 MQTT 的云控制平台，可实现物联网网关的轻松配置和可扩展部署。',
      featureImage: '/images/goodcloud/features/feature-centralized.jpg', // 新增特色大图路径
    },
    {
      id: 'gc2',
      title: '实时设备监控',
      description:
        'GoodCloud仪表盘提供了一个自上而下的视图，显示了注册物联网设备的所有网络流量。通过这种简化的网络监控使您能够快速识别注册物联网设备的意外问题。',
      featureImage: '/images/goodcloud/features/feature-monitoring.jpg', // 新增特色大图路径
    },
    {
      id: 'gc3',
      title: '设备分组和创建模板',
      description:
        '对物联网设备进行分组以实现快速部署和批量配置。 允许快速扩展物联网设备并启用批量固件升级。',
      featureImage: '/images/goodcloud/features/feature-template.jpg', // 新增特色大图路径
    },
    {
      id: 'gc4',
      title: '远程访问',
      description:
        'GoodCloud 为所有已注册的 IoT 设备提供对设备 Web 管理面板的远程访问。 借助其远程 SSH 功能，您可以从任何地方访问已注册的树莓派。',
      featureImage: '/images/goodcloud/features/feature-remote.jpg', // 新增特色大图路径
    },
    {
      id: 'gc5',
      title: '站点到站点协作',
      description:
        '在多个分支机构之间建立安全隧道以进行内部通信，并在远程工作时与您的团队成员协作。',
      featureImage: '/images/goodcloud/features/feature-s2s.jpg', // 新增特色大图路径
    },
    {
      id: 'gc6',
      title: 'GPS追踪',
      description:
        'GPS 追踪功能目前仅适用于有定制需求的客户。 我们提供内置 GPS 模块的 4G LTE 物联网网关，用户可以从 GoodCloud 查看 GPS 路由数据。',
      featureImage: '/images/goodcloud/features/feature-gps.jpg', // 新增特色大图路径
    },
  ];

  const goodCloudBenefits = [
    {
      id: 'b1',
      title: '降低运营和维护成本',
      description:
        'GoodCloud大大降低了企业和公共部门的运营风险，因为它是一个现成的 平台，用户不必冒险投入大量资金和时间来自行开发和维护一个平台。',
      imageUrl: '/images/goodcloud/cost-reduction.png',
      imageAlt: '降低运营和维护成本',
    },
    {
      id: 'b2',
      title: '为决策者提供一目了然的见解',
      description:
        '所有重要数据都显示在用户友好且视觉上令人愉悦的仪表板中，用户可以一目了然地获得关键见解，并最终帮助用户在更短的时间内做出更好的决策。',
      imageUrl: '/images/goodcloud/dashboard.png',
      imageAlt: 'user-friendly and visually-pleasing dashboard',
    },
  ];

  const pricingPlans = [
    {
      id: 'basic',
      title: '基本',
      features: [
        { text: '远程访问（http、终端）', enabled: true },
        { text: '网络优化', enabled: true },
        { text: '区域分组', enabled: true },
        { text: 'S2S (虚拟组网)', enabled: true },
        { text: '批次管理', enabled: false },
        { text: '数据分析', enabled: false },
      ],
      ctaText: '免费试用',
      ctaLink: '/free-trial',
      buttonStyle: 'filled',
    },
    {
      id: 'enterprise',
      title: '企业',
      features: [
        { text: '远程访问（客户端设备）', enabled: true },
        { text: '网络优化', enabled: true },
        { text: '区域分组', enabled: true },
        { text: 'S2S (虚拟组网)', enabled: true },
        { text: '批次管理', enabled: true },
        { text: '数据分析', enabled: true },
      ],
      ctaText: '请求演示',
      ctaLink: '/request-demo',
      buttonStyle: 'outline',
    },
    {
      id: 'onpremise',
      title: '内部部署',
      features: [
        { text: '安装在私人服务器上', enabled: true },
        { text: '白标方案', enabled: true },
        { text: '固件开发', enabled: true },
        { text: '自定义用户角色', enabled: true },
        { text: '定制解决方案', enabled: true },
      ],
      ctaText: '解锁更多功能',
      ctaLink: '/contact',
      buttonStyle: 'outline',
    },
  ];

  return (
    <>
      <SEO
        title="GoodCloud物联网设备管理系统"
        description="物联网项目的设备管理、数据收集、处理和可视化。GoodCloud是一个基于MQTT的云控制平台，可实现物联网网关的轻松配置和可扩展部署。"
        keywords="GoodCloud, 物联网设备管理, IoT管理平台, 远程设备管理, 物联网网关, MQTT, 云平台"
        ogImage="/images/goodcloud/og-image.jpg"
        canonicalUrl="https://www.gl-inet.com/solutions/goodcloud"
        structuredData={[articleSchema, breadcrumbSchema]}
      />

      <div className="w-full overflow-hidden">
        <GoodCloudHero imageUrl="/images/goodcloud/hero-background.jpg" />

        {/* 主要介绍部分 */}
        <section className="py-20 text-center bg-white">
          <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-bold mb-8">让远程IoT网关管理简单化</h2>
            <p className="text-lg leading-relaxed text-gray-700 mb-10 max-w-4xl mx-auto">
              GoodCloud是一个物联网网关管理平台，通过授权用户远程部署和管理所有连接中的物联网网关。
              将网络设备集中组织到云平台进行批量管理，只需要将网络配置设成范本，轻松套用到其他网关，即可进行大规模部署和软件升级。
            </p>
            <div className="my-10 max-w-3xl mx-auto">
              <img
                src="/images/goodcloud/goodcloud-diagram-cn.jpg"
                alt="GoodCloud系统架构图"
                className="max-w-full h-auto"
              />
            </div>
            <div className="flex justify-center">
              <Link
                href="/docs/goodcloud"
                className="inline-block bg-transparent text-black py-4 px-8 rounded-full font-semibold uppercase tracking-wider border-2 border-black transition hover:bg-black hover:text-white"
              >
                阅读 GOODCLOUD 文档
              </Link>
            </div>
          </div>
        </section>

        {/* GoodCloud核心功能部分 */}
        <GoodCloudFeatureGrid
          sectionTitle="一站式物联网设备管理系统"
          features={goodCloudFeatures}
        />

        {/* 两个价值主张部分 */}
        <BenefitSection benefits={goodCloudBenefits} />

        {/* 套餐计划部分 */}
        <section className="py-20 bg-white">
          <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-bold mb-12">选择您的计划</h2>
            <div className="flex flex-col sm:flex-col md:flex-row justify-center items-stretch gap-5">
              {pricingPlans.map(plan => (
                <div
                  key={plan.id}
                  className="bg-white border border-gray-200 p-8 text-center flex-1 flex flex-col justify-between max-w-md hover:shadow-lg transition-shadow"
                >
                  <div>
                    <h3 className="text-2xl font-medium mb-5">{plan.title}</h3>
                    <ul className="list-none p-0 mb-8 text-left min-h-[230px]">
                      {plan.features.map((feature, index) => (
                        <li
                          key={index}
                          className={`flex items-start py-2 ${!feature.enabled ? 'text-gray-400' : 'text-gray-800'}`}
                        >
                          <span
                            className={`mr-3 font-bold ${!feature.enabled ? 'text-gray-400' : 'text-teal-500'}`}
                          >
                            {feature.enabled ? '✓' : '×'}
                          </span>
                          <span>{feature.text}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Link
                    href={plan.ctaLink}
                    className={`inline-block py-4 px-8 rounded-full font-extrabold text-base transition border-2 max-w-xs mx-auto ${plan.buttonStyle === 'filled' ? 'bg-cyan-500 text-white border-cyan-500 hover:bg-cyan-600 hover:border-cyan-600' : 'bg-white text-black border-black hover:bg-black hover:text-white'}`}
                  >
                    {plan.ctaText}
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 联系信息部分 */}
        <section className="py-10 md:py-16 bg-white">
          <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-extrabold mb-5">寻找解决方案？</h2>
            <div className="mt-5">
              <a
                href="tel:4008089202"
                className="inline-block bg-cyan-500 text-white py-4 px-10 rounded-full text-lg font-semibold transition hover:bg-cyan-600"
              >
                请拨打 ************
              </a>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
