import Head from 'next/head';
import Link from 'next/link';
import MediaPartners from '@/components/MediaPartners';

export default function SolutionsPage() {
  const solutions = [
    {
      id: 'iot',
      title: '物联网',
      description: '“网关-云端”物联网解决方案',
      imageUrl: '/images/solutions/solution-iot.jpg',
      link: '/solutions/iot-solutions', // 假设的链接，可替换
    },
    {
      id: 'goodcloud',
      title: 'GoodCloud 云平台',
      description: '物联网项目的设备管理、数据收集、处理和可视化',
      imageUrl: '/images/solutions/solution-goodcloud.jpg',
      link: '/solutions/goodcloud',
    },
    {
      id: 'sdwan',
      title: '简化的 SDWAN 解决方案',
      description: '安全地远程访问您的内部业务网络',
      imageUrl: '/images/solutions/solution-sdwan.jpg',
      link: '/solutions/sdwan-solutions', // 假设的链接，可替换
    },
  ];

  return (
    <>
      <Head>
        <title>方案中心 - GL.iNet</title>
        <meta
          name="description"
          content="探索 GL.iNet 为您的业务量身打造的一流制造和网络解决方案。"
        />
      </Head>

      <div className="font-sans">
        {/* 面包屑导航 */}
        <div className="bg-white text-black py-4 px-4 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
          <Link href="/" className="text-green-600 hover:underline">
            首页
          </Link>{' '}
          / 方案中心
        </div>

        {/* Hero 区域 */}
        <section
          className="bg-cover bg-center text-white py-20 px-8 text-center relative"
          style={{
            backgroundImage:
              "linear-gradient(rgba(10, 25, 41, 0.75), rgba(10, 25, 41, 0.75)), url('/images/solutions/hero-background.jpeg')",
          }}
        >
          <div className="relative z-10 max-w-4xl mx-auto">
            <h1 className="text-4xl sm:text-3xl sm:text-3xl sm:text-4xl md:text-5xl font-bold mb-4 leading-tight">
              方案中心
            </h1>
            <h2 className="text-lg sm:text-lg sm:text-lg md:text-xl font-normal mb-8 opacity-90">
              我们的工程师努力为您的定制需求创建一流的制造解决方案
            </h2>
            <Link
              href="/contact"
              className="inline-block bg-transparent border-2 border-white text-white py-4 px-10 rounded-full font-semibold text-base text-center transition-colors duration-300 hover:bg-white hover:text-black"
            >
              联系我们的销售经理
            </Link>
          </div>
        </section>

        {/* 方案展示区域 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-screen-xl mx-auto px-4">
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
              {solutions.map(solution => (
                <div
                  key={solution.id}
                  className="group bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg relative w-full h-[312px]"
                >
                  <img
                    src={solution.imageUrl}
                    alt={solution.title}
                    className="w-full h-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-[1.02] group-hover:translate-x-1"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 z-10 transition-opacity duration-300 group-hover:bg-opacity-40"></div>
                  <div className="absolute inset-0 flex flex-col justify-start p-8 text-white z-20 transition-transform duration-300 transform group-hover:translate-y-4">
                    <h3 className="text-2xl font-extrabold uppercase mb-4">
                      {solution.title}
                    </h3>
                    <p className="text-base font-bold leading-relaxed mb-5">
                      {solution.description}
                    </p>
                    <Link
                      href={solution.link}
                      className="self-start inline-block text-base font-medium text-white no-underline uppercase tracking-wider border-b border-white pb-1 transition-colors hover:text-gray-300 hover:border-gray-300"
                    >
                      了解更多 &gt;
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 合作伙伴区域 */}
        <MediaPartners title="合作伙伴" />
      </div>
    </>
  );
}
