import Link from 'next/link';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import SEO from '@/components/SEO';
import { generateProductSchema, generateBreadcrumbSchema } from '@/utils/structuredData';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import {
  faArrowLeft,
  faArrowRight,
  faCheckCircle,
  faMicrochip,
  faMemory,
  faHdd,
  faWifi,
  faEthernet,
  faBolt,
  faPlug,
  faTemperatureHalf,
  faThLarge,
  faDesktop,
  faObjectGroup,
  faHouseUser,
  faHandshake,
  faDownload,
  faMobileScreenButton,
  faFileLines,
} from '@fortawesome/free-solid-svg-icons';
import { x300bData } from '../../data/x300b-data';
import Footer from '../../components/Footer';

// 动态导入技术规格组件
const TechnicalSpecifications = dynamic(
  () => import('../../components/TechnicalSpecifications'),
  {
    loading: () => (
      <div className="py-16 bg-white text-center">加载技术规格中...</div>
    ),
    ssr: true,
  }
);

const iconMap = {
  faMicrochip,
  faMemory,
  faHdd,
  faWifi,
  faEthernet,
  faBolt,
  faPlug,
  faTemperatureHalf,
  faThLarge,
  faDesktop,
  faObjectGroup,
  faHouseUser,
  faHandshake,
  faArrowRight,
  faDownload,
  faMobileScreenButton,
  faFileLines,
};

const technicalSpecsData = [
  {
    label: '接口',
    value:
      '1 x 电源接口\n1 x WAN 以太网接口\n1 x LAN 以太网接口\n1 x 标准 SIM 卡槽\n1 x 重置按钮',
  },
  { label: 'CPU', value: 'QCA9531, @650MHz SoC' },
  { label: '存储', value: 'DDR2 128MB / NOR Flash 16MB' },
  {
    label: '外置天线',
    value:
      'GL-X300B-BLE：1 x 2.4G 天线，1 x LTE 天线，1 x BLE 天线\nGL-X300B-GPS：2 x 2.4G 天线，2 x LTE 天线，1 x GPS 天线\nGL-X300B-RS485: 2 x 2.4G Antennas, 1 x 4G LTE Antennas',
  },
  { label: 'Wi-Fi 协议', value: 'IEEE 802.11b/g/n' },
  {
    label: 'Wi-Fi 速率',
    value:
      'GL-X300B-BLE: 150Mbps (2.4GHz)\nGL-X300B-GPS: 300Mbps (2.4GHz)\nGL-X300B-RS485: 300Mbps (2.4GHz)',
  },
  { label: '以太网口', value: '10/100Mbps' },
  {
    label: '以太网速度',
    value:
      'GL-X300B-BLE: 150Mbps (2.4GHz) / GL-X300B-GPS: 300Mbps (2.4GHz) / GL-X300B-RS485: 300Mbps (2.4GHz)',
  },
  { label: 'LED', value: '电源 / Wi-Fi / 4G / WAN 状态指示' },
  {
    label: '电源输入',
    value:
      '支持9-35VDC宽电压输入，电源接口形态：5.5 mm DC 电源连接器或接线端子\nDC5521, 12V/1A (5.5*2.1mm) [默认适配器] / 9~36V',
  },
  { label: '功耗', value: '<4W' },
  { label: '工作温度', value: '-20 ~ 55°C' },
  { label: '存储温度', value: '-30 ~ 70°C' },
  { label: '尺寸 / 重量', value: '104 x 113 x 28mm / 235g' },
  { label: '工具和下载', value: 'downloads_placeholder' },
];

export default function X300BPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Combine both GPS and BLE images for a single carousel
  const currentCarouselImages = [
    ...x300bData.carousel.gps,
    ...x300bData.carousel.ble,
  ];

  // 生成结构化数据
  const productSchema = generateProductSchema({
    name: x300bData.basic.name,
    description: x300bData.basic.description,
    imageUrl: `https://www.gl-inet.com${x300bData.basic.mainImage}`,
    category: '4G路由器',
  });

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: '首页', url: 'https://www.gl-inet.com' },
    { name: '产品中心', url: 'https://www.gl-inet.com/products' },
    { name: x300bData.basic.name, url: `https://www.gl-inet.com/products/x300b` },
  ]);

  const nextSlide = () => {
    setCurrentSlide(prev =>
      prev === currentCarouselImages.length - 1 ? 0 : prev + 1
    );
  };

  const prevSlide = () => {
    setCurrentSlide(prev =>
      prev === 0 ? currentCarouselImages.length - 1 : prev - 1
    );
  };

  return (
    <>
      <SEO
        title={`${x300bData.basic.name} - ${x300bData.basic.title}`}
        description={x300bData.basic.description}
        keywords="GL-X300B, Collie, 4G路由器, 物联网网关, LTE路由器, 工业路由器, OpenWrt"
        ogImage={`/images/products/x300b/og-image.jpg`}
        ogType="product"
        canonicalUrl="https://www.gl-inet.com/products/x300b"
        structuredData={[productSchema, breadcrumbSchema]}
      />

      <div className="min-h-screen bg-white">
        {/* Breadcrumbs */}
        <div className="bg-white text-black p-4 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
          <Link
            href="/"
            className="text-green-600 no-underline hover:underline"
          >
            首页
          </Link>
          <span className="text-gray-500 mx-2">/</span>
          <Link
            href="/products"
            className="text-green-600 no-underline hover:underline"
          >
            产品中心
          </Link>
          <span className="text-gray-500 mx-2">/</span>
          <span className="text-gray-800">{x300bData.basic.name}</span>
        </div>

        {/* Hero Section */}
        <section className="relative bg-[linear-gradient(to_right,#155799,#159957)] text-white pt-2 pb-5">
          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 z-10 w-full">
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-center">
              {/* Image on the left, transformed down */}
              <div className="text-center transform translate-y-12">
                <Image
                  src={x300bData.basic.mainImage}
                  alt={x300bData.basic.name}
                  width={500}
                  height={400}
                  className="max-w-full h-auto"
                  priority
                />
              </div>
              {/* Text on the right */}
              <div className="py-8">
                <p className="text-gray-200 text-lg mb-2">
                  {x300bData.basic.subtitle}
                </p>
                <h1 className="text-6xl font-bold mb-4">
                  {x300bData.basic.name}
                </h1>
                <h2 className="text-3xl text-gray-200 mb-8">
                  {x300bData.basic.title}
                </h2>
                <Link
                  href={`mailto:${x300bData.basic.contactEmail}`}
                  className="inline-block bg-white text-black py-4 px-8 rounded-full font-semibold text-lg hover:text-gray-500 transition-colors"
                >
                  联系销售
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* New Two-Column Section with Carousel */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-center">
              {/* Left Column: Text */}
              <div>
                <h2 className="text-4xl font-semibold text-gray-800 mb-4">
                  {x300bData.basic.title}
                </h2>
                <p className="text-gray-600 mb-8 leading-relaxed">
                  {x300bData.basic.description}
                </p>
                {/* Divider */}
                <div className="my-6 border-t border-gray-200"></div>
                {/* Features List */}
                <div className="space-y-6 text-gray-700">
                  <p className="text-xl">
                    内置{' '}
                    <span
                      className="text-2xl font-bold"
                      style={{ color: 'rgb(23, 162, 184)' }}
                    >
                      4G LTE
                    </span>{' '}
                    模块
                  </p>
                  <p className="text-xl">
                    专为{' '}
                    <span
                      className="text-2xl font-bold"
                      style={{ color: 'rgb(23, 162, 184)' }}
                    >
                      固定物联网
                    </span>{' '}
                    或{' '}
                    <span
                      className="text-2xl font-bold"
                      style={{ color: 'rgb(23, 162, 184)' }}
                    >
                      车辆位置跟踪
                    </span>{' '}
                    而打造
                  </p>
                  <p className="text-xl">
                    内置{' '}
                    <span
                      className="text-2xl font-bold"
                      style={{ color: 'rgb(23, 162, 184)' }}
                    >
                      硬件看门狗
                    </span>
                  </p>
                </div>

                {/* Learn More Button */}
                <div className="mt-8">
                  <Link
                    href="#"
                    className="inline-block text-white py-4 px-8 rounded-full font-semibold text-lg transition-colors hover:opacity-90"
                    style={{ backgroundColor: 'rgb(23, 162, 184)' }}
                  >
                    了解更多
                  </Link>
                </div>
              </div>

              {/* Right Column: Carousel */}
              <div className="relative group">
                <button
                  className="absolute top-1/2 -translate-y-1/2 -left-4 z-10 text-gray-400 hover:text-gray-800 w-12 h-12 transition-all"
                  onClick={prevSlide}
                >
                  <FontAwesomeIcon icon={faChevronLeft} size="2x" />
                </button>
                <div className="overflow-hidden rounded-xl">
                  <div
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                  >
                    {currentCarouselImages.map(image => (
                      <div key={image.id} className="min-w-full flex-shrink-0">
                        <Image
                          src={image.image}
                          alt={image.alt}
                          width={600}
                          height={500}
                          className="w-full h-auto block max-h-[500px] object-contain mx-auto"
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <button
                  className="absolute top-1/2 -translate-y-1/2 -right-4 z-10 text-gray-400 hover:text-gray-800 w-12 h-12 transition-all"
                  onClick={nextSlide}
                >
                  <FontAwesomeIcon icon={faChevronRight} size="2x" />
                </button>
                {/* Dashed Line Indicator */}
                <div className="flex justify-center items-center gap-2 mt-6">
                  {currentCarouselImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className="w-8 h-2 flex items-center"
                    >
                      <div
                        className={`w-full h-0.5 transition-all ${index === currentSlide ? 'bg-cyan-500' : 'bg-gray-300'}`}
                      ></div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Comparison Table Section */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div>
              <div className="grid grid-cols-[118px_1fr_1fr_1fr]">
                {/* Empty corner */}
                <div className="p-4"></div>
                {/* Model Headers */}
                {x300bData.comparison.models.map(model => (
                  <div key={model.name} className="p-4 text-center">
                    <img
                      src={model.image}
                      alt={model.name}
                      className="w-48 mx-auto mb-2"
                    />
                    <h4 className="font-semibold">{model.name}</h4>
                  </div>
                ))}
              </div>
              {/* Feature Rows */}
              {x300bData.comparison.features.map((feature, featureIndex) => (
                <div
                  key={feature.name}
                  className={`grid grid-cols-[118px_1fr_1fr_1fr] items-center border-gray-200 ${featureIndex > 0 ? 'border-t' : ''}`}
                >
                  <div
                    className="p-4 font-semibold text-white h-full flex items-center"
                    style={{ backgroundColor: 'rgb(20, 144, 164)' }}
                  >
                    {feature.name}
                  </div>
                  {feature.values.map((value, valueIndex) => (
                    <div
                      key={valueIndex}
                      className="p-4 text-center border-l border-gray-200 h-full flex items-center justify-center"
                      style={{
                        backgroundColor:
                          valueIndex === 1
                            ? 'rgb(232, 232, 232)'
                            : 'rgb(239, 239, 239)',
                      }}
                    >
                      {typeof value === 'boolean' && value ? (
                        <FontAwesomeIcon
                          icon={faCheckCircle}
                          className="text-green-500 text-2xl"
                        />
                      ) : (
                        <span>{value}</span>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Powerful Antenna Section */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-semibold text-gray-800 mb-12">
              {x300bData.powerfulAntenna.title}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-[1fr_auto_1fr] gap-x-8 gap-y-6 items-start">
              {/* Left Text */}
              <p className="md:text-justify text-gray-600 leading-relaxed md:w-[315.75px] md:justify-self-end mt-40">
                <strong className="font-extrabold text-gray-800">
                  {
                    x300bData.powerfulAntenna.descriptions.find(
                      d => d.position === 'left'
                    ).model
                  }
                </strong>{' '}
                {
                  x300bData.powerfulAntenna.descriptions.find(
                    d => d.position === 'left'
                  ).text
                }
              </p>
              {/* Center Image */}
              <img
                src={x300bData.powerfulAntenna.image}
                alt="Antenna configuration"
                className="max-w-md md:max-w-lg mx-auto"
              />
              {/* Right Text */}
              <p className="md:text-justify text-gray-600 leading-relaxed md:w-[315.75px] md:justify-self-start mt-40">
                <strong className="font-extrabold text-gray-800">
                  {
                    x300bData.powerfulAntenna.descriptions.find(
                      d => d.position === 'right'
                    ).model
                  }
                </strong>{' '}
                {
                  x300bData.powerfulAntenna.descriptions.find(
                    d => d.position === 'right'
                  ).text
                }
              </p>
            </div>
            {/* Bottom Text in its own container */}
            <div className="mt-8 max-w-[315.75px] mx-auto">
              <p className="text-justify text-gray-600 leading-relaxed">
                <strong className="font-extrabold text-gray-800">
                  {
                    x300bData.powerfulAntenna.descriptions.find(
                      d => d.position === 'bottom'
                    ).model
                  }
                </strong>{' '}
                {
                  x300bData.powerfulAntenna.descriptions.find(
                    d => d.position === 'bottom'
                  ).text
                }
              </p>
            </div>
          </div>
        </section>

        {/* Installation Section with Video */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-semibold text-gray-800 text-center mb-12">
              {x300bData.installation.title}
            </h2>
            <div className="space-y-16">
              {/* Wall Mount: Video Left, Text Right */}
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="mb-4">
                  <video muted autoPlay loop className="w-full aspect-video">
                    <source
                      src={x300bData.installation.methods[0].video}
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="text-left">
                  <h3 className="text-2xl font-semibold text-gray-800 mb-2">
                    {x300bData.installation.methods[0].title}
                  </h3>
                  <p className="text-base leading-relaxed text-gray-600">
                    {x300bData.installation.methods[0].description}
                  </p>
                </div>
              </div>
              {/* DIN Rail: Text Left, Video Right */}
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="md:order-last mb-4">
                  <video muted autoPlay loop className="w-full aspect-video">
                    <source
                      src={x300bData.installation.methods[1].video}
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="md:order-first text-left">
                  <h3 className="text-2xl font-semibold text-gray-800 mb-2">
                    {x300bData.installation.methods[1].title}
                  </h3>
                  <p className="text-base leading-relaxed text-gray-600">
                    {x300bData.installation.methods[1].description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* GoodCloud Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-center">
              {/* Left Column */}
              <div className="text-left">
                <h2 className="text-4xl font-semibold text-gray-800">
                  {x300bData.goodCloud.title}
                </h2>
                <h3 className="text-3xl text-gray-600 mt-2 mb-8">
                  {x300bData.goodCloud.subtitle}
                </h3>

                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-8">
                  {x300bData.goodCloud.features.map(feature => (
                    <div key={feature.text} className="text-left">
                      <FontAwesomeIcon
                        icon={iconMap[feature.icon]}
                        className="text-black fa-3x mb-4"
                      />
                      <p className="font-medium text-gray-700">
                        {feature.text}
                      </p>
                    </div>
                  ))}
                </div>

                <Link
                  href={x300bData.goodCloud.link}
                  className="text-black hover:text-gray-500 font-medium inline-flex items-center underline underline-offset-4"
                >
                  <span>了解更多关于 GoodCloud 云平台 &gt;</span>
                </Link>
              </div>
              {/* Right Column */}
              <div className="text-center pl-12">
                <Image
                  src={x300bData.goodCloud.imageUrl}
                  alt="GoodCloud Platform"
                  width={400}
                  height={300}
                  className="max-w-full h-auto rounded-lg transform scale-125"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Enterprise Solutions Section */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-semibold text-gray-800 text-center mb-12">
              {x300bData.enterpriseSolutions.title}
            </h2>
            <div className="flex justify-center gap-4 mb-12 flex-wrap">
              {x300bData.enterpriseSolutions.tabs.map((tabTitle, index) => (
                <button
                  key={index}
                  className={`py-4 px-6 rounded-full font-medium transition-all duration-300 ${activeTab === index ? 'bg-[rgb(23,162,184)] text-white' : 'bg-white text-[rgb(23,162,184)] hover:bg-gray-100'}`}
                  onClick={() => setActiveTab(index)}
                >
                  {tabTitle}
                </button>
              ))}
            </div>
            <div className="bg-white p-8 md:p-12 min-h-[500px]">
              {activeTab === 0 && (
                <div className="text-center">
                  <h3 className="text-3xl font-semibold text-gray-800 mb-4">
                    {x300bData.enterpriseSolutions.customization.title}
                  </h3>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-10">
                    {x300bData.enterpriseSolutions.customization.description}
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
                    {x300bData.enterpriseSolutions.customization.steps.map(
                      (step, index) => (
                        <div key={index} className="text-center">
                          <img
                            src={step.image}
                            alt={step.title}
                            className="w-60 h-auto mx-auto mb-4"
                          />
                          <h4 className="font-bold text-gray-800">
                            {step.title}
                          </h4>
                        </div>
                      )
                    )}
                  </div>
                  <Link
                    href="#contact-us"
                    className="text-lg text-black hover:text-gray-500 font-medium underline underline-offset-4"
                  >
                    联系销售 &gt;
                  </Link>
                </div>
              )}
              {activeTab === 1 && (
                <div className="text-center">
                  <h3 className="text-3xl font-semibold text-gray-800 mb-4">
                    {x300bData.enterpriseSolutions.gpsTracking.title}
                  </h3>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-10">
                    {x300bData.enterpriseSolutions.gpsTracking.description}
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                    {x300bData.enterpriseSolutions.gpsTracking.scenarios.map(
                      (scenario, index) => (
                        <div key={index} className="text-left">
                          <img
                            src={scenario.image}
                            alt={scenario.title}
                            className="w-full rounded-lg mb-4"
                          />
                          <h4 className="text-lg font-bold text-gray-800 mb-2">
                            {scenario.title}
                          </h4>
                          <p className="text-gray-600">
                            {scenario.description}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                  <Link
                    href="#contact-us"
                    className="text-lg text-black hover:text-gray-500 font-medium underline underline-offset-4"
                  >
                    联系销售 &gt;
                  </Link>
                </div>
              )}
              {activeTab === 2 && (
                <div className="text-center">
                  <h3 className="text-3xl font-semibold text-gray-800 mb-4">
                    {x300bData.enterpriseSolutions.bleIot.title}
                  </h3>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-10">
                    {x300bData.enterpriseSolutions.bleIot.description}
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                    {x300bData.enterpriseSolutions.bleIot.scenarios.map(
                      (scenario, index) => (
                        <div key={index} className="text-left">
                          <img
                            src={scenario.image}
                            alt={scenario.title}
                            className="w-full rounded-lg mb-4"
                          />
                          <h4 className="text-lg font-bold text-gray-800 mb-2">
                            {scenario.title}
                          </h4>
                          <p className="text-gray-600">
                            {scenario.description}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                  <div className="flex justify-center items-center gap-6 flex-wrap">
                    <Link
                      href="/solutions/gateway-to-cloud-iot-solution-for-smes"
                      className="text-lg text-black hover:text-gray-500 font-medium underline underline-offset-4"
                    >
                      了解更多我们物联网解决方案 &gt;
                    </Link>
                    <Link
                      href="#contact-us"
                      className="text-lg text-black hover:text-gray-500 font-medium underline underline-offset-4"
                    >
                      联系销售 &gt;
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Product Features Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 text-left">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-8 mb-4">
              {x300bData.productFeatures.map((feature, index) => (
                <div key={index} className="text-center p-4">
                  <img
                    src={feature.icon}
                    alt={feature.title}
                    className="w-180 h-180 mx-auto mb-4"
                  />
                  <h2 className="font-semibold text-gray-800 mb-2">
                    {feature.title}
                  </h2>
                </div>
              ))}
            </div>
            {/* Feature Notes */}
            {x300bData.productFeatureNotes &&
              x300bData.productFeatureNotes.length > 0 && (
                <div className="max-w-3xl mx-auto text-center p-4 mt-8">
                  <p className="text-base text-black-600">
                    {x300bData.productFeatureNotes.map((note, index) => (
                      <span key={index}>
                        {note}
                        {index < x300bData.productFeatureNotes.length - 1 && (
                          <span className="mx-4">|</span>
                        )}
                      </span>
                    ))}
                  </p>
                </div>
              )}
          </div>
        </section>

        {/* Technical Specs Section */}
        <section id="specs" className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-semibold text-gray-800 text-left mb-12">
              技术规格
            </h2>
            <div className="divide-y divide-gray-200 border-t border-gray-200">
              {technicalSpecsData.map((spec, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4">
                  <div className="px-6 py-4 font-semibold bg-gray-50 text-gray-700 flex items-center">
                    {spec.label}
                  </div>
                  <div className="px-6 py-4 md:col-span-3 text-black-600">
                    {spec.value === 'downloads_placeholder' ? (
                      <div className="flex items-center space-x-6">
                        <a
                          href={x300bData.downloads.firmware}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-black hover:text-gray-500 font-medium inline-flex items-center space-x-2 pb-1 border-b border-black hover:border-gray-500"
                        >
                          <FontAwesomeIcon icon={faDownload} />
                          <span>固件</span>
                        </a>
                        <span>|</span>
                        <a
                          href={x300bData.downloads.app}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-black hover:text-gray-500 font-medium inline-flex items-center space-x-2 pb-1 border-b border-black hover:border-gray-500"
                        >
                          <FontAwesomeIcon icon={faMobileScreenButton} />
                          <span>App</span>
                        </a>
                        <span>|</span>
                        <a
                          href={x300bData.downloads.documentation}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-black hover:text-gray-500 font-medium inline-flex items-center space-x-2 pb-1 border-b border-black hover:border-gray-500"
                        >
                          <FontAwesomeIcon icon={faFileLines} />
                          <span>文档</span>
                        </a>
                      </div>
                    ) : (
                      <div className="whitespace-pre-line">{spec.value}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact-us" className="py-16 bg-gray-50 text-center">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-semibold text-gray-800 mb-8">
              寻找解决方案？
            </h2>
            <div className="mt-8">
              <a
                href={`tel:${x300bData.contact.phone}`}
                className="inline-block bg-cyan-500 hover:bg-cyan-600 text-white py-4 px-8 rounded-full font-medium text-lg transition-colors"
              >
                请拨打 {x300bData.contact.phone}
              </a>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
