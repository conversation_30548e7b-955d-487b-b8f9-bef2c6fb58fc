import { useState, Fragment } from 'react';
import ProductGridCard from '../../components/ProductGridCard';
import Pagination from '../../components/Pagination';
import { productsData } from '../../data/products';
import Link from 'next/link';
// import styles from '../../styles/ProductsPage.module.css'; // Removed CSS Modules import

const PRODUCTS_PER_PAGE = 6;

export default function ProductsPage() {
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(productsData.length / PRODUCTS_PER_PAGE);

  const indexOfLastProduct = currentPage * PRODUCTS_PER_PAGE;
  const indexOfFirstProduct = indexOfLastProduct - PRODUCTS_PER_PAGE;
  const currentProducts = productsData.slice(
    indexOfFirstProduct,
    indexOfLastProduct
  );

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber);
    // Optionally, scroll to top of the product list on page change
    if (typeof window !== 'undefined') {
      window.scrollTo({
        top: 0, // Or scroll to the top of the product grid
        behavior: 'smooth',
      });
    }
  };

  return (
    <>
      <div className="bg-white text-black py-4 px-4 border-b border-gray-200 text-left text-sm max-w-6xl mx-auto">
        <Link href="/" className="text-green-600 hover:underline">
          首页
        </Link>{' '}
        / 产品中心
      </div>

      <section className="bg-gradient-to-b from-blue-800 to-sky-300 text-white py-16 px-8 text-center relative">
        <div className="relative z-10 max-w-3xl mx-auto">
          <h1 className="text-4xl sm:text-4xl sm:text-4xl sm:text-4xl sm:text-4xl sm:text-4xl md:text-5xl font-bold mb-4 leading-tight">
            产品中心
          </h1>
          <p className="text-lg sm:text-lg sm:text-lg sm:text-lg sm:text-lg sm:text-lg md:text-xl font-normal mb-8 opacity-90">
            探索我们为您的家庭、旅行和业务提供的全系列创新网络解决方案。
          </p>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8 py-16">
        {productsData.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 sm:grid-cols-1 sm:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 sm:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
              {currentProducts.map(product => (
                <ProductGridCard key={product.id} product={product} />
              ))}
            </div>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </>
        ) : (
          <p className="text-center text-xl text-gray-500 mt-12">
            当前没有产品可显示。
          </p>
        )}
      </div>
    </>
  );
}
