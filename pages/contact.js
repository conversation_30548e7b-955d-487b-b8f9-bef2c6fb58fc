import Head from 'next/head';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEnvelope,
  faHeadset,
  faPhoneAlt,
} from '@fortawesome/free-solid-svg-icons';

export default function ContactPage() {
  return (
    <>
      <Head>
        <title>联系我们 - GL.iNet</title>
        <meta
          name="description"
          content="联系 GL.iNet 获取产品销售与技术支持信息"
        />
      </Head>

      <div className="font-sans">
        <div className="bg-white text-black p-4 border-b border-gray-200 text-left text-sm max-w-5xl mx-auto">
          <Link
            href="/"
            className="text-green-600 no-underline hover:underline"
          >
            首页
          </Link>{' '}
          / 联系我们
        </div>
        <div
          className="text-center py-16 px-4 text-white"
          style={{
            background:
              'linear-gradient(-45deg,#3c9ebc 20%,#009e86 50%,#002400 100%)',
            background:
              '-webkit-linear-gradient(-45deg,#3c9ebc 20%,#009e86 50%,#002400 100%)',
          }}
        >
          <h1 className="text-5xl font-semibold mb-4 text-white">联系我们</h1>
          <h5 className="text-lg font-normal mb-0 text-white">
            欢迎您来咨询，我们将竭诚为您服务！
          </h5>
        </div>

        <div className="flex flex-col sm:flex-col sm:flex-col sm:flex-col sm:flex-col md:flex-row justify-center text-center bg-white py-8 px-8">
          <div className="flex-1 max-w-sm p-4 sm:p-6 bg-white border border-gray-200 rounded shadow-sm my-2 mx-2">
            <div className="text-6xl mb-4 text-gray-800">
              <FontAwesomeIcon icon={faEnvelope} />
            </div>
            <h5 className="text-xl text-gray-800 font-semibold mb-4">
              销售邮件
            </h5>
            <p className="text-base text-gray-600 mb-2">
              <a
                href="mailto:<EMAIL>"
                className="text-green-600 no-underline hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
          <div className="flex-1 max-w-sm p-4 sm:p-6 bg-white border border-gray-200 rounded shadow-sm my-2 mx-2">
            <div className="text-6xl mb-4 text-gray-800">
              <FontAwesomeIcon icon={faHeadset} />
            </div>
            <h5 className="text-xl text-gray-800 font-semibold mb-4">
              技术支持
            </h5>
            <p className="text-base text-gray-600 mb-2">
              <a
                href="mailto:<EMAIL>"
                className="text-green-600 no-underline hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
          <div className="flex-1 max-w-sm p-4 sm:p-6 bg-white border border-gray-200 rounded shadow-sm my-2 mx-2">
            <div className="text-6xl mb-4 text-gray-800">
              <FontAwesomeIcon icon={faPhoneAlt} />
            </div>
            <h5 className="text-xl text-gray-800 font-semibold mb-4">
              全国销售热线
            </h5>
            <a
              href="tel:+************"
              className="text-green-600 no-underline hover:underline"
            >
              ************
            </a>
          </div>
        </div>
        {/* 参考网页中底部有一个二维码，页脚中已包含，此处不再重复 */}
      </div>
    </>
  );
}
