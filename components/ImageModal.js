import { useEffect } from 'react';
import Image from 'next/image';

export default function ImageModal({ imageUrl, imageAlt, onClose }) {
  // 处理ESC键关闭模态框
  useEffect(() => {
    const handleEscKey = event => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);

    // 防止背景滚动
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [onClose]);

  // 点击背景关闭模态框
  const handleBackdropClick = event => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!imageUrl) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-[1000] p-4 sm:p-6 md:p-10 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div className="relative max-w-[90vw] max-h-[90vh] md:max-w-[95vw] md:max-h-[95vh] bg-white rounded-xl overflow-hidden shadow-2xl animate-modal-fade-in">
        <button
          className="absolute top-4 right-4 bg-black bg-opacity-60 text-white border-none rounded-full w-9 h-9 md:w-10 md:h-10 text-xl sm:text-xl sm:text-xl sm:text-xl md:text-2xl cursor-pointer flex items-center justify-center z-[1001] transition-colors hover:bg-opacity-80 backdrop-blur-sm"
          onClick={onClose}
        >
          ×
        </button>
        <div className="w-full h-full flex items-center justify-center">
          <Image
            src={imageUrl}
            alt={imageAlt || '放大图片'}
            width={800}
            height={600}
            className="max-w-full max-h-full w-auto h-auto rounded-xl"
            style={{ objectFit: 'contain' }}
          />
        </div>
      </div>
    </div>
  );
}
