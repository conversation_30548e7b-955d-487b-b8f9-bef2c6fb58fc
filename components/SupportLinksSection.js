import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHeadset,
  faComments,
  faShoppingCart,
} from '@fortawesome/free-solid-svg-icons';

const supportLinksData = [
  {
    id: 'tech-support',
    title: '技术支援',
    description: '告诉我们该怎么帮助你！',
    link: '/support',
    icon: faHeadset,
  },
  {
    id: 'forum',
    title: '论坛',
    description: '有关于我们的问题吗？',
    link: 'https://forum.gl-inet.com/',
    isExternal: true,
    icon: faComments,
  },
  {
    id: 'store',
    title: '商店',
    description: '立即购买！',
    link: 'https://store.gl-inet.com/',
    isExternal: true,
    icon: faShoppingCart,
  },
];

export default function SupportLinksSection() {
  return (
    <section className="py-10 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8 grid grid-cols-1 md:grid-cols-3 gap-8">
        {supportLinksData.map(item => (
          <div key={item.id} className="text-center p-4 sm:p-6">
            <Link
              href={item.link}
              className="no-underline text-current group"
              target={item.isExternal ? '_blank' : '_self'}
              rel={item.isExternal ? 'noopener noreferrer' : ''}
            >
              <div className="text-6xl mb-5 text-gray-800 transition-colors duration-300">
                <FontAwesomeIcon icon={item.icon} />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 transition-colors duration-300 group-hover:text-gray-500">
                {item.title}
              </h3>
            </Link>
            <p className="text-base text-black mb-6">{item.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
}
