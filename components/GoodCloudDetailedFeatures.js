import DetailedFeatureItem from './DetailedFeatureItem';

export default function GoodCloudDetailedFeatures({ sectionTitle, features }) {
  const defaultFeatures = [
    {
      title: '端到端加密',
      description:
        '所有设备与云之间的通信都经过强大的加密协议保护，确保您的数据在传输过程中的绝对安全。',
      imageUrl: '/images/solutions/goodcloud/feature-encryption.jpg',
      imageAlt: '数据加密示意图',
    },
    {
      title: '灵活的设备管理',
      description:
        '通过我们的分层管理系统，您可以将设备分配给不同的客户或项目，并为每个层级设置精细的访问权限。',
      imageUrl: '/images/solutions/goodcloud/feature-management.jpg',
      imageAlt: '设备管理界面',
      reverseLayout: true,
    },
    {
      title: '实时警报和通知',
      description:
        '当设备离线、性能异常或出现安全事件时，系统会立即通过电子邮件或应用内通知向您发送警报，让您始终掌握网络状态。',
      imageUrl: '/images/solutions/goodcloud/feature-alerts.jpg',
      imageAlt: '实时警报通知',
    },
  ];

  const featuresToRender = features || defaultFeatures;

  return (
    <section className="py-16 bg-white">
      <div className="max-w-6xl mx-auto px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800">
            {sectionTitle || '探索 GoodCloud 的强大功能'}
          </h2>
        </div>
        <div>
          {featuresToRender.map((feature, index) => (
            <DetailedFeatureItem
              key={index}
              title={feature.title}
              description={feature.description}
              imageUrl={feature.imageUrl}
              imageAlt={feature.imageAlt}
              reverseLayout={feature.reverseLayout}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
