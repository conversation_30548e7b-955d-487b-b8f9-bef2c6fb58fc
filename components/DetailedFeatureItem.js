import Image from 'next/image';

export default function DetailedFeatureItem({
  title,
  description,
  imageUrl,
  imageAlt,
  reverseLayout = false,
}) {
  const layoutClasses = reverseLayout ? 'sm:flex-col md:flex-row-reverse' : 'sm:flex-col md:flex-row';

  return (
    <div
      className={`flex flex-col ${layoutClasses} items-center gap-8 sm:gap-8 md:gap-12 mb-16`}
    >
      {/* 图片部分 */}
      <div className="w-full sm:w-full sm:w-full md:w-1/2">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={imageAlt || title || 'Feature image'}
            width={500}
            height={400}
            className="rounded-lg shadow-xl object-cover w-full h-auto"
          />
        ) : (
          <div className="w-full h-[400px] bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
            图片占位符
          </div>
        )}
      </div>

      {/* 文字部分 */}
      <div className="w-full sm:w-full sm:w-full md:w-1/2 text-left">
        <h3 className="text-2xl sm:text-2xl md:text-2xl font-bold text-gray-800 mb-4">
          {title || '功能标题'}
        </h3>
        <p className="text-base sm:text-base md:text-base text-gray-600 leading-relaxed">
          {description ||
            '这里是关于这个功能的详细描述。详细解释它的工作原理、优点以及它如何为用户解决特定问题。'}
        </p>
      </div>
    </div>
  );
}
