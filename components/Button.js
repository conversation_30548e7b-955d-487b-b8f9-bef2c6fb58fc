import Link from 'next/link';

const variants = {
  primary: 'bg-cyan-500 text-white hover:bg-cyan-600',
  secondary: 'bg-gray-700 text-white hover:bg-gray-800',
  outline:
    'bg-transparent border-2 border-black text-black hover:bg-black hover:text-white',
  link: 'text-blue-600 hover:text-blue-800 underline',
  // 新增项目中常用的按钮样式
  tab: 'bg-gray-100 text-[#17a2b8] hover:bg-gray-200',
  tabActive: 'bg-cyan-500 text-white',
  white: 'bg-white text-black hover:bg-gray-300',
  whiteLight: 'bg-white text-black hover:bg-gray-200',
  black: 'bg-black text-white hover:bg-gray-800',
  ghost: 'bg-transparent text-gray-700 hover:bg-cyan-500/10',
};

const sizes = {
  small: 'py-2 px-4 text-sm',
  medium: 'py-4 px-6 text-base',
  large: 'py-4 px-8 text-base',
  largeText: 'py-4 px-8 text-lg',
  // 新增项目中使用的尺寸 - 标准化间距
  xlarge: 'py-6 px-10 text-base',
  xlargeText: 'py-4 px-10 text-lg',
  tab: 'px-24 py-4 text-base',
  tabSmall: 'px-8 py-4 text-base',
};

export default function Button({
  children,
  href,
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'medium',
  className = '',
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  role,
  tabIndex,
  ...props
}) {
  const baseClasses =
    'font-semibold rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 inline-block text-center';
  const variantClasses = variants[variant] || variants.primary;
  const sizeClasses = sizes[size] || sizes.medium;
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  const combinedClasses =
    `${baseClasses} ${variantClasses} ${sizeClasses} ${disabledClasses} ${className}`.trim();

  // 可访问性属性
  const accessibilityProps = {
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    role: role,
    tabIndex: tabIndex,
  };

  // 过滤掉 undefined 值
  const filteredAccessibilityProps = Object.fromEntries(
    Object.entries(accessibilityProps).filter(([_, value]) => value !== undefined)
  );

  if (href && !disabled) {
    return (
      <Link
        href={href}
        className={combinedClasses}
        {...filteredAccessibilityProps}
        {...props}
      >
        {children}
      </Link>
    );
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={combinedClasses}
      {...filteredAccessibilityProps}
      {...props}
    >
      {children}
    </button>
  );
}
