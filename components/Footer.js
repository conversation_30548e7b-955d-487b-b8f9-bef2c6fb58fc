import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMobileAlt } from '@fortawesome/free-solid-svg-icons';

const footerNavs = {
  products: [
    { label: '开发板', href: '/products/dev-boards' },
    { label: '旅行路由', href: '/products/travel-routers' },
    { label: '家用路由', href: '/products/home-routers' },
    { label: '工业网关', href: '/products/industrial-gateways' },
    { label: '4G LTE 路由网关', href: '/products/4g-lte-gateways' },
    { label: '软路由', href: '/products/soft-routers' },
    { label: '停产产品', href: '/products/discontinued' },
  ],
  solutions: [
    { label: '蜂窝网络连接', href: '/solutions/cellular-connectivity' },
    { label: 'GoodCloud 云平台', href: '/solutions/goodcloud' },
    { label: '简化版SDWAN', href: '/solutions/sdwan' },
    { label: '白标及客制化服务', href: '/solutions/customization' },
  ],
  community: [
    { label: '论坛', href: '/community/forum' },
    { label: '文档', href: '/community/docs' },
    { label: '固件发布', href: '/community/firmware-releases' },
    { label: 'GitHub', href: 'https://github.com/gl-inet' },
    { label: 'Apps', href: '/community/apps' },
    { label: '加入我们', href: '/join' },
    { label: '英文官网', href: 'https://www.gl-inet.com/' },
  ],
  contact: [
    // 联系我们部分的内容将在右侧单独显示
  ],
};

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#2a2a2a] text-gray-400 pt-12 pb-5 text-[13px] leading-relaxed">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 主要导航区域 */}
        <div className="mb-6 pb-6">
          <div className="flex flex-wrap justify-between gap-10">
            {/* Nav Columns */}
            <div className="flex-grow sm:flex-grow-0 basis-full sm:basis-1/2 md:basis-1/6 min-w-[160px]">
              <h4 className="text-white text-lg font-bold mb-3 tracking-wide">
                产品中心
              </h4>
              <ul className="list-none p-0 m-0">
                {footerNavs.products.map(item => (
                  <li key={item.label} className="mb-1.5">
                    <Link
                      href={item.href}
                      className="text-teal-500 no-underline hover:text-teal-600 hover:underline"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div className="flex-grow sm:flex-grow-0 basis-full sm:basis-1/2 md:basis-1/6 min-w-[160px]">
              <h4 className="text-white text-lg font-bold mb-3 tracking-wide">
                解决方案
              </h4>
              <ul className="list-none p-0 m-0">
                {footerNavs.solutions.map(item => (
                  <li key={item.label} className="mb-1.5">
                    <Link
                      href={item.href}
                      className="text-teal-500 no-underline hover:text-teal-600 hover:underline"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div className="flex-grow sm:flex-grow-0 basis-full sm:basis-1/2 md:basis-1/6 min-w-[160px]">
              <h4 className="text-white text-lg font-bold mb-3 tracking-wide">
                社区
              </h4>
              <ul className="list-none p-0 m-0">
                {footerNavs.community.map(item => (
                  <li key={item.label} className="mb-1.5">
                    <Link
                      href={item.href}
                      className="text-teal-500 no-underline hover:text-teal-600 hover:underline"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Column */}
            <div className="flex-grow sm:flex-grow-0 basis-full sm:basis-full md:basis-1/2 lg:basis-1/3 min-w-[320px]">
              <h4 className="text-white text-base font-bold mb-3 tracking-wide">
                联系我们
              </h4>
              <div className="mt-3 space-y-4">
                <p className="text-white font-normal">
                  全国销售热线{' '}
                  <a
                    href="tel:************"
                    className="text-teal-500 no-underline hover:underline"
                  >
                    ************
                  </a>
                </p>
                <p className="text-white font-normal">
                  <span>销售邮件 </span>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-teal-500 no-underline hover:underline"
                  >
                    <EMAIL>
                  </a>{' '}
                  |<span> 技术支持 </span>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-teal-500 no-underline hover:underline"
                  >
                    <EMAIL>
                  </a>
                </p>

                <div className="space-y-1">
                  <h4 className="text-white text-base font-bold mb-3 tracking-wide">
                    深圳总部
                  </h4>
                  <div className="flex items-start">
                    <FontAwesomeIcon
                      icon={faMobileAlt}
                      className="w-3 mt-1 mr-2"
                    />
                    <span>深圳市宝安区石岩街道松白路创维数字大厦3楼</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <h4 className="text-white text-base font-bold mb-3 tracking-wide">
                    分公司
                  </h4>
                  <div className="flex items-start">
                    <FontAwesomeIcon
                      icon={faMobileAlt}
                      className="w-3 mt-1 mr-2"
                    />
                    <span>香港科学园科技大道西5W大楼6楼601室</span>
                  </div>
                  <div className="flex items-start">
                    <FontAwesomeIcon
                      icon={faMobileAlt}
                      className="w-3 mt-1 mr-2"
                    />
                    <span>
                      美国 Suite 216, 10400 Eaton Place, Fairfax, VA 22030
                    </span>
                  </div>
                  <div className="flex items-start">
                    <FontAwesomeIcon
                      icon={faMobileAlt}
                      className="w-3 mt-1 mr-2"
                    />
                    <span>成都市高新区天府五街200号菁蓉汇1B栋204室</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <h4 className="text-white text-base font-bold mb-3 tracking-wide">
                    智造工厂
                  </h4>
                  <div className="flex items-start">
                    <FontAwesomeIcon
                      icon={faMobileAlt}
                      className="w-3 mt-1 mr-2"
                    />
                    <span>深圳市宝安区石岩水田第四工业区阿宝工业园A栋2F</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部版权和社交媒体 */}
        <div className="flex justify-between items-center pt-5 flex-col sm:flex-col md:flex-row gap-4">
          <div className="text-xs text-gray-500 text-center md:text-left">
            <p className="mb-5">
              Copyright © {currentYear} 深圳市广联智通科技有限公司
            </p>
            <p>
              <Link
                href="https://beian.miit.gov.cn/"
                className="text-gray-500 font-normal no-underline text-base hover:underline underline-offset-4"
              >
                粤ICP备18130956号
              </Link>
            </p>
          </div>

          <div className="flex gap-2.5 items-center">
            <a
              href="#"
              aria-label="Bilibili"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              B
            </a>
            <a
              href="#"
              aria-label="Douyin"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              D
            </a>
            <a
              href="#"
              aria-label="Xiaohongshu"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              X
            </a>
            <a
              href="#"
              aria-label="Toutiao"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              T
            </a>
            <a
              href="#"
              aria-label="GitHub"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              G
            </a>
            <a
              href="#"
              aria-label="WeChat"
              className="w-7 h-7 bg-gray-700 rounded-sm text-center leading-7 text-gray-400 no-underline transition-all hover:bg-cyan-500 hover:text-white hover:-translate-y-px"
            >
              W
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
