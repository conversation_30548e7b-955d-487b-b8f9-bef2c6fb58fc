import Link from 'next/link';
import Image from 'next/image';

// 定义按钮主题的 Tailwind CSS 类映射
const buttonThemes = {
  buttonThemeDefault: 'bg-gray-800 text-white hover:bg-gray-700',
  buttonThemeBlack: 'bg-black text-white hover:bg-gray-800',
  buttonThemeWhite: 'bg-white text-black hover:bg-gray-200',
  buttonThemeBlue: 'bg-blue-600 text-white hover:bg-blue-700',
};



// 定义文本颜色主题的 Tailwind CSS 类映射
const textThemes = {
  textWhite: 'text-white',
  textBlack: 'text-black',
};

export default function HeroSlide({ slide }) {
  const { imageUrl, title, subtitle, buttonText, buttonLink, theme } = slide;

  const currentTextColor = theme
    ? textThemes[theme.contentTextColorClass] || textThemes.textWhite
    : textThemes.textWhite;

  const currentButtonTheme = theme
    ? buttonThemes[theme.buttonThemeClass] || buttonThemes.buttonThemeDefault
    : buttonThemes.buttonThemeDefault;

  return (
    <div className="min-w-full flex-shrink-0 h-[400px] sm:h-[500px] md:h-[600px] relative flex items-center justify-start overflow-hidden">
      <Image
        src={imageUrl}
        alt={title || 'Carousel background'}
        fill
        sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, 100vw"
        className="object-cover object-center z-10"
        priority
      />
      <div
        className={`relative z-20 py-4 sm:py-6 bg-transparent max-w-xs sm:max-w-md md:max-w-lg ml-[5%] sm:ml-[8%] md:ml-[10%] text-left ${currentTextColor}`}
      >
        {title && (
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold leading-tight mb-3 sm:mb-4 text-shadow-md">
            {title}
          </h1>
        )}
        {subtitle && (
          <p className="text-base sm:text-lg sm:text-lg sm:text-lg md:text-xl leading-relaxed mb-6 sm:mb-8 font-medium text-shadow">
            {subtitle}
          </p>
        )}
        {buttonText && buttonLink && (
          <Link
            href={buttonLink}
            className={`
              inline-flex items-center justify-center
              h-11 sm:h-12 md:h-14
              min-w-[140px] sm:min-w-[160px] md:min-w-[200px]
              px-6 sm:px-8 md:px-12
              text-sm sm:text-base md:text-base
              rounded-[22px] sm:rounded-3xl md:rounded-[28px]
              font-bold no-underline
              transition-all duration-300
              border-none outline-none cursor-pointer
              whitespace-nowrap
              ${currentButtonTheme}
            `.replace(/\s+/g, ' ').trim()}
          >
            {buttonText}
          </Link>
        )}
      </div>
    </div>
  );
}
