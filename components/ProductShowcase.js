import Link from 'next/link';
import ProductHighlightCard from './ProductHighlightCard';
import { productsData } from '../data/products';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/free-solid-svg-icons';

export default function ProductShowcase() {
  // 只展示前4个产品在首页
  const displayProducts = productsData.slice(0, 4);

  return (
    <section className="py-16 text-center bg-gray-50">
      <div className="w-11/12 mx-auto px-4">
        <h2 className="text-3xl font-bold mb-4 text-gray-800">
          IoT 智能网关及解决方案供应商
        </h2>
        <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          GL.iNet深耕于网络硬件和软件领域，为客户提供安全可靠的网络设备和IoT解决方案。
        </p>
        <div className="mb-10">
          <Link
            href="/about-us"
            className="text-gray-800 no-underline uppercase tracking-wider border-b border-gray-800 pb-1 transition-colors hover:text-gray-500 hover:border-gray-500 text-base font-medium"
          >
            了解更多 &gt;
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-16">
          {displayProducts.map(product => (
            <ProductHighlightCard key={product.id} product={product} />
          ))}
        </div>

        <div className="flex flex-col items-center gap-5">
          <div className="flex justify-center">
            <Link
              href="/products/compare"
              className="inline-block py-4 px-8 border border-gray-800 text-gray-800 bg-white no-underline rounded-full font-semibold text-sm transition-all hover:bg-black hover:text-white hover:border-black"
            >
              产品参数对比
            </Link>
          </div>
          <div className="flex flex-col sm:flex-row justify-center items-center gap-6 sm:gap-12">
            <Link
              href="/products/all"
              className="text-gray-800 no-underline font-medium inline-flex items-center border-b border-gray-800 pb-0.5 gap-2 hover:text-gray-500 hover:border-gray-500"
            >
              所有产品 &gt;
            </Link>
            <Link
              href="/downloads/brochure"
              className="text-gray-800 no-underline font-medium inline-flex items-center border-b border-gray-800 pb-0.5 gap-2 hover:text-gray-500 hover:border-gray-500"
            >
              <FontAwesomeIcon icon={faDownload} className="h-4 w-4" />{' '}
              下载产品手册
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
