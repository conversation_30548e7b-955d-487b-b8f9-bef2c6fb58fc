import Link from 'next/link';
import Image from 'next/image';

export default function ProductHighlightCard({ product }) {
  const { name, imageUrl, imageUrlHover, description, link } = product;

  return (
    <Link href={link} className="block no-underline text-current group">
      <div className="relative w-full h-[450px] overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg transition-colors duration-300 group-hover:bg-gray-100">
        {/* Default Image */}
        <Image
          src={imageUrl}
          alt={name}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-contain transition-opacity duration-300 opacity-100 group-hover:opacity-0"
          priority
        />
        {/* Hover Image */}
        <Image
          src={imageUrlHover || imageUrl}
          alt={`${name} hover`}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover transition-opacity duration-300 opacity-0 group-hover:opacity-100"
        />

        {/* Text overlay */}
        <div className="absolute inset-0 flex flex-col justify-start items-center text-center pt-12 px-6 z-10 pointer-events-none">
          <h3 className="text-xl font-bold text-black leading-tight mb-2">
            {name}
          </h3>
          {description && (
            <p className="text-sm font-normal text-black leading-snug mb-4">
              {description}
            </p>
          )}
        </div>
      </div>
    </Link>
  );
}