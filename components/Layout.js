import Head from 'next/head';
import Header from './Header'; // 取消注释
import Footer from './Footer'; // 取消注释

export default function Layout({ children, title = 'GL.iNet Clone' }) {
  return (
    <div className="flex flex-col min-h-screen">
      <Head>
        <title>{title}</title>
        <meta
          name="description"
          content="GL.iNet website clone using Next.js"
        />
        <link rel="icon" href="/favicon.ico" />{' '}
        {/* 假设 public 目录下有 favicon.ico */}
      </Head>
      <Header /> {/* 取消注释并使用 Header 组件 */}
      <main className="flex-grow">{children}</main>
      <Footer /> {/* 取消注释并使用 Footer 组件 */}
    </div>
  );
}
