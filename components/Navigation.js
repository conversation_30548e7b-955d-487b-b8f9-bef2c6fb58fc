import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';
import DropdownMenu from './DropdownMenu';

export default function Navigation({ navItems }) {
  const [openDropdown, setOpenDropdown] = useState(null); // 用于跟踪哪个下拉菜单是打开的
  const navRef = useRef(null); // 用于点击外部关闭下拉菜单

  const handleMouseEnter = label => {
    const item = navItems.find(item => item.label === label);
    if (item && item.children) {
      setOpenDropdown(label);
    }
  };

  const handleMouseLeave = () => {
    // 简化版：任何时候鼠标离开整个li区域都关闭
    setOpenDropdown(null);
  };

  const handleClickOutside = event => {
    if (navRef.current && !navRef.current.contains(event.target)) {
      setOpenDropdown(null);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav ref={navRef} role="navigation" aria-label="主导航">
      <ul className="flex items-center space-x-6 list-none m-0 p-0">
        {navItems.map(item => (
          <li
            key={item.label}
            className="relative"
            onMouseEnter={() => handleMouseEnter(item.label)}
            onMouseLeave={handleMouseLeave}
          >
            <Link
              href={item.href}
              className="flex items-center no-underline text-gray-500 py-2 px-2 transition-colors duration-300 hover:text-black focus:outline-none"
              aria-expanded={item.children ? (openDropdown === item.label ? 'true' : 'false') : undefined}
              aria-haspopup={item.children ? 'true' : undefined}
            >
              {item.label}
              {item.children && (
                <span
                  className="ml-1 text-xs transform transition-transform duration-200 group-hover:rotate-180"
                  aria-hidden="true"
                >
                  ▼
                </span>
              )}
            </Link>
            {item.children && openDropdown === item.label && (
              <DropdownMenu items={item.children} />
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
}
