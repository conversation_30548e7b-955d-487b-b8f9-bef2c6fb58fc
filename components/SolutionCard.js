import Link from 'next/link';

export default function SolutionCard({ solution }) {
  const { title, description, imageUrl, link } = solution;

  return (
    <div className="group bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg relative w-full max-w-[345px] h-[312px]">
      <img
        src={imageUrl}
        alt={title}
        className="w-full h-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-[1.02] group-hover:translate-x-1"
      />

      <div className="absolute inset-0 bg-black bg-opacity-50 z-10 transition-opacity duration-300 group-hover:bg-opacity-40"></div>

      <div className="absolute inset-0 flex flex-col justify-start p-8 text-white z-20 transition-transform duration-300 transform group-hover:translate-y-4">
        <h3 className="text-2xl font-extrabold uppercase mb-4">{title}</h3>
        <p className="text-base font-semibold leading-relaxed mb-5">
          {description}
        </p>
        <Link
          href={link}
          className="self-start inline-block py-4 px-10 border border-white rounded-full text-xs font-bold no-underline transition-colors hover:bg-white hover:text-black"
        >
          了解更多
        </Link>
      </div>
    </div>
  );
}
