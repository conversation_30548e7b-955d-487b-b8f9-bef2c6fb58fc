import SeparatedFeatureItem from './SeparatedFeatureItem';

export default function GoodCloudFeatureGrid({ sectionTitle, features }) {
  const defaultFeatures = [
    {
      id: '1',
      title: '远程设备管理',
      description: '随时随地访问和管理您的网络设备。',
      link: '#',
    },
    {
      id: '2',
      title: '实时监控与告警',
      description: '获取设备状态的实时更新和即时告警通知。',
      link: '#',
    },
    {
      id: '3',
      title: '批量操作与配置',
      description: '轻松对大量设备进行固件升级和配置更改。',
      link: '#',
    },
    {
      id: '4',
      title: '安全数据通道',
      description: '通过加密通道保护您的设备数据和管理访问。',
      link: '#',
    },
    // 您可以根据参考页面或实际需求增加更多占位符功能
  ];

  const displayFeatures = features || defaultFeatures;

  return (
    <section className="py-10 sm:py-12 md:py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {sectionTitle && (
          <h2 className="text-center text-2xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl font-bold text-gray-800 mb-10 sm:mb-10 md:mb-12">
            {sectionTitle}
          </h2>
        )}
        {!sectionTitle && (
          <h2 className="text-center text-2xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl font-bold text-gray-800 mb-10 sm:mb-10 md:mb-12">
            【核心功能区块标题占位符】
          </h2>
        )}
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-8 md:gap-10">
          {displayFeatures.map(feature => (
            <SeparatedFeatureItem
              key={feature.id}
              title={feature.title}
              description={feature.description}
              featureImage={feature.featureImage}
              link={feature.link}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
