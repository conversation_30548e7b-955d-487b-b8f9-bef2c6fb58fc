import { useState, useEffect, useCallback } from 'react';

export default function GenericCarousel({
  items = [],
  autoPlay = true,
  autoPlayInterval = 5000,
  showIndicators = true,
  showNavigation = false,
  className = '',
  children,
}) {
  const [currentSlide, setCurrentSlide] = useState(0);

  const goToNextSlide = useCallback(() => {
    setCurrentSlide(prev => (prev + 1) % items.length);
  }, [items.length]);

  const goToPrevSlide = useCallback(() => {
    setCurrentSlide(prev => (prev - 1 + items.length) % items.length);
  }, [items.length]);

  const goToSlide = useCallback(index => {
    setCurrentSlide(index);
  }, []);

  // 自动轮播效果
  useEffect(() => {
    if (!autoPlay || items.length <= 1) return;

    const timer = setInterval(goToNextSlide, autoPlayInterval);
    return () => clearInterval(timer);
  }, [goToNextSlide, autoPlay, autoPlayInterval, items.length]);

  if (items.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* 轮播内容 */}
      <div className="relative overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {items.map((item, index) => (
            <div key={item.id || index} className="min-w-full flex-shrink-0">
              {children ? (
                children(item, index)
              ) : (
                <div className="w-full h-64 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Slide {index + 1}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 导航按钮 */}
      {showNavigation && items.length > 1 && (
        <>
          <button
            onClick={goToPrevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-200 z-10"
            aria-label="Previous slide"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            onClick={goToNextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-200 z-10"
            aria-label="Next slide"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </>
      )}

      {/* 指示器 */}
      {showIndicators && items.length > 1 && (
        <div className="flex justify-center items-center gap-2 mt-4">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className="w-8 h-2 flex items-center cursor-pointer"
              aria-label={`Go to slide ${index + 1}`}
            >
              <div
                className={`w-full h-0.5 transition-all duration-300 ${
                  currentSlide === index ? 'bg-gray-500' : 'bg-gray-300'
                }`}
              ></div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
