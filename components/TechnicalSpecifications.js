import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMicrochip,
  faMemory,
  faHdd,
  faWifi,
  faNetworkWired,
  faPlug,
  faThermometerHalf,
  faRulerCombined,
  faWeight,
} from '@fortawesome/free-solid-svg-icons';

const iconMap = {
  faMicrochip,
  faMemory,
  faHdd,
  faWifi,
  faNetworkWired,
  faPlug,
  faThermometerHalf,
  faRulerCombined,
  faWeight,
};

export default function TechnicalSpecifications({ specifications }) {
  if (!specifications || specifications.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl font-bold text-center mb-12 text-gray-800">
          技术规格
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {specifications.map((spec, index) => (
            <div
              key={index}
              className="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-4">
                {spec.icon && iconMap[spec.icon] && (
                  <FontAwesomeIcon
                    icon={iconMap[spec.icon]}
                    className="text-blue-600 text-2xl mr-3"
                  />
                )}
                <h3 className="text-xl font-semibold text-gray-800">
                  {spec.category}
                </h3>
              </div>
              <div className="space-y-2">
                {spec.details.map((detail, detailIndex) => (
                  <div key={detailIndex} className="flex justify-between">
                    <span className="text-gray-600">{detail.label}:</span>
                    <span className="text-gray-800 font-medium">
                      {detail.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
