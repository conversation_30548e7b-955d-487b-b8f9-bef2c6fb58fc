import Link from 'next/link';
import { useState } from 'react';

export default function MobileNavigation({ navItems, isOpen, onClose }) {
  const [openSubmenu, setOpenSubmenu] = useState(null);

  const handleSubmenuToggle = (label) => {
    setOpenSubmenu(openSubmenu === label ? null : label);
  };

  const handleLinkClick = () => {
    onClose();
    setOpenSubmenu(null);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* 侧边栏菜单 */}
      <div className="fixed top-0 right-0 h-50 w-80 max-w-[85vw] bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out md:hidden">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">菜单</h2>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              aria-label="关闭菜单"
            >
              <svg
                className="w-6 h-6 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* 导航内容 */}
          <nav className="flex-1 overflow-y-auto py-4">
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.label}>
                  {item.children ? (
                    // 有子菜单的项目
                    <div>
                      <button
                        onClick={() => handleSubmenuToggle(item.label)}
                        className="w-full flex items-center justify-between px-4 py-4 text-left text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        aria-expanded={openSubmenu === item.label}
                      >
                        <span className="font-medium">{item.label}</span>
                        <svg
                          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                            openSubmenu === item.label ? 'rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>
                      
                      {/* 子菜单 */}
                      {openSubmenu === item.label && (
                        <ul className="bg-gray-50 border-l-2 border-gray-200">
                          {item.children.map((child) => (
                            <li key={child.label}>
                              <Link
                                href={child.href}
                                onClick={handleLinkClick}
                                className="block px-8 py-2.5 text-gray-600 hover:text-cyan-500 hover:bg-white transition-colors duration-200"
                              >
                                {child.label}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    // 普通链接项目
                    <Link
                      href={item.href}
                      onClick={handleLinkClick}
                      className="block px-4 py-4 text-gray-700 font-medium hover:bg-gray-50 hover:text-cyan-500 transition-colors duration-200"
                    >
                      {item.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
}
