import Link from 'next/link';
import { useState } from 'react';

export default function MobileNavigation({ navItems, isOpen, onClose }) {
  const [openSubmenu, setOpenSubmenu] = useState(null);

  const handleSubmenuToggle = (label) => {
    setOpenSubmenu(openSubmenu === label ? null : label);
  };

  const handleLinkClick = () => {
    onClose();
    setOpenSubmenu(null);
  };

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className={`fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden transition-opacity duration-300 ease-in-out ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* 全宽下拉菜单 */}
      <div
        className={`fixed top-[73px] left-0 right-0 bg-white shadow-xl z-50 transition-all duration-300 ease-in-out md:hidden ${
          isOpen ? 'max-h-[calc(100vh-73px)] opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
        }`}
        style={{
          visibility: isOpen ? 'visible' : 'hidden',
          transform: isOpen ? 'translateY(0)' : 'translateY(-10px)'
        }}
      >
        {/* 导航内容 */}
        <nav className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 73px)' }}>
          <div className="py-2">
            <ul className="space-y-1">
              {navItems && navItems.map((item) => (
                <li key={item.label}>
                  {item.children ? (
                    // 有子菜单的项目
                    <div>
                      <button
                        onClick={() => handleSubmenuToggle(item.label)}
                        className="w-full flex items-center justify-between px-4 py-4 text-left text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        aria-expanded={openSubmenu === item.label}
                      >
                        <span className="font-medium">{item.label}</span>
                        <svg
                          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                            openSubmenu === item.label ? 'rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>

                      {/* 子菜单 */}
                      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        openSubmenu === item.label ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                      }`}>
                        <ul className="bg-gray-50 border-l-2 border-gray-200">
                          {item.children.map((child) => (
                            <li key={child.label}>
                              <Link
                                href={child.href}
                                onClick={handleLinkClick}
                                className="block px-8 py-2.5 text-gray-600 hover:text-cyan-500 hover:bg-white transition-colors duration-200"
                              >
                                {child.label}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ) : (
                    // 普通链接项目
                    <Link
                      href={item.href}
                      onClick={handleLinkClick}
                      className="block px-4 py-4 text-gray-700 font-medium hover:bg-gray-50 hover:text-cyan-500 transition-colors duration-200"
                    >
                      {item.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </nav>
      </div>
    </>
  );
}
