import { useState } from 'react';
import Image from 'next/image';
import ImageModal from './ImageModal';

export default function SeparatedFeatureItem({
  title,
  description,
  featureImage,
  link,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleImageClick = () => {
    if (featureImage) {
      setIsModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const featureLink = link || '#';

  return (
    <>
      <div className="flex flex-col items-start text-left w-full h-full md:mb-0 mb-8">
        {/* 图片部分 */}
        <div className="w-full mb-4">
          {featureImage ? (
            <div
              className="relative cursor-pointer overflow-hidden inline-block w-full"
              onClick={handleImageClick}
            >
              <Image
                src={featureImage}
                alt={title || 'Feature image'}
                width={400}
                height={250}
                className="w-full h-auto block"
                priority
              />
            </div>
          ) : (
            <div className="w-full h-[250px] bg-gray-100 flex items-center justify-center text-gray-500 text-base border-2 border-dashed border-gray-300">
              【功能图片占位符】
            </div>
          )}
        </div>

        {/* 文字部分 */}
        <div className="w-full flex flex-col flex-grow">
          <div className="flex-grow">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center">
              {title || '【功能标题占位符】'}
            </h3>
            <p className="text-sm text-gray-700 leading-relaxed mb-4">
              {description || '【功能描述占位符】'}
            </p>
          </div>
        </div>
      </div>

      {/* 图片模态框 */}
      {isModalOpen && (
        <ImageModal
          imageUrl={featureImage}
          imageAlt={title}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
}
