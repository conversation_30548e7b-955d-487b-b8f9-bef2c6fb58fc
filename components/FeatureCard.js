import Image from 'next/image';

export default function FeatureCard({ title, description, featureImage }) {
  return (
    <div className="bg-white rounded-lg shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 overflow-hidden flex flex-col h-full">
      {featureImage && (
        <div className="w-full relative">
          <Image
            src={featureImage}
            alt={title || 'Feature image'}
            width={320}
            height={180}
            className="w-full h-auto object-cover"
            priority
          />
        </div>
      )}
      <div className="p-4 sm:p-6 md:p-6 text-left flex-grow flex flex-col">
        <h3 className="text-lg sm:text-lg sm:text-lg sm:text-lg md:text-xl font-bold mb-2.5 text-gray-800">
          {title || '【功能标题占位符】'}
        </h3>
        <p className="text-sm sm:text-sm md:text-base text-gray-600 leading-relaxed flex-grow">
          {description || '【功能描述占位符】'}
        </p>
      </div>
    </div>
  );
}
