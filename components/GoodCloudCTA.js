import Button from './Button';

export default function GoodCloudCTA({
  title,
  description,
  primaryButton,
  secondaryButton,
}) {
  const defaultPrimaryButton = {
    text: '免费试用',
    href: '#',
  };

  const defaultSecondaryButton = {
    text: '联系我们',
    href: '#',
  };

  const currentPrimaryButton = { ...defaultPrimaryButton, ...primaryButton };
  const currentSecondaryButton = {
    ...defaultSecondaryButton,
    ...secondaryButton,
  };

  return (
    <section className="bg-gray-100 py-16">
      <div className="max-w-4xl mx-auto px-8 text-center">
        <h2 className="text-3xl font-bold mb-4">
          {title || '准备好开始了吗？'}
        </h2>
        <p className="text-lg text-gray-700 mb-8">
          {description ||
            '立即体验 GoodCloud 的强大功能，或联系我们的专家获取更多信息。'}
        </p>
        <div className="flex gap-4 justify-center">
          {currentPrimaryButton.href && (
            <Button
              href={currentPrimaryButton.href}
              variant="primary"
              size="large"
            >
              {currentPrimaryButton.text}
            </Button>
          )}
          {currentSecondaryButton.href && (
            <Button
              href={currentSecondaryButton.href}
              variant="outline"
              size="large"
            >
              {currentSecondaryButton.text}
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}
