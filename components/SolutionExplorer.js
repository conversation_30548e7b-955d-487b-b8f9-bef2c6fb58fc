import SectionTitle from './SectionTitle';
import SolutionCard from './SolutionCard';
import { solutionsData } from '../data/solutions';

export default function SolutionExplorer() {
  return (
    <section className="py-16 bg-gray-100">
      <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8">
        <SectionTitle
          title="探索不同领域的 GL.iNet 产品"
          subtitle="我们提供多样化的网络解决方案，满足各种应用场景的需求。"
        />
        <div className="mt-12 grid justify-items-center gap-8 grid-cols-1 sm:grid-cols-1 sm:grid-cols-1 sm:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
          {solutionsData.map(solution => (
            <SolutionCard key={solution.id} solution={solution} />
          ))}
        </div>
      </div>
    </section>
  );
}
