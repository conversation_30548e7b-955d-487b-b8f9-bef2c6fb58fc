import Link from 'next/link';

export default function DropdownMenu({ items }) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <ul className="absolute top-full right-0 bg-white border border-gray-200 list-none m-0 py-2 min-w-[240px] z-[1001] shadow-lg rounded-md transition-all duration-200 ease-out opacity-100 transform-none">
      {items.map(item => (
        <li key={item.label}>
          <Link
            href={item.href}
            className="block py-2 px-4 text-gray-700 no-underline whitespace-nowrap hover:bg-gray-100 hover:text-cyan-500"
          >
            {item.label}
          </Link>
        </li>
      ))}
    </ul>
  );
}
