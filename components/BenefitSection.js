import Image from 'next/image';

// 单个价值主张组件
function BenefitItem({ title, description, imageUrl, imageAlt, reverse }) {
  return (
    <div
      className={`
      flex flex-col sm:flex-col lg:flex-row items-center justify-between gap-8 sm:gap-8 sm:gap-10 lg:gap-12 mb-20 last:mb-0
      ${reverse ? 'lg:flex-row-reverse' : ''}
    `}
    >
      <div className="flex-1 max-w-2xl text-center sm:text-center sm:text-center lg:text-left">
        <h3 className="text-2xl sm:text-2xl sm:text-2xl sm:text-2xl md:text-3xl font-bold mb-5 text-gray-800">
          {title}
        </h3>
        <p className="text-base sm:text-base sm:text-base sm:text-base md:text-lg leading-relaxed text-gray-600">
          {description}
        </p>
      </div>
      {imageUrl && (
        <div className="flex-1 flex justify-center items-center">
          <Image
            src={imageUrl}
            alt={imageAlt || title}
            width={500}
            height={300}
            className="max-w-full h-auto"
          />
        </div>
      )}
    </div>
  );
}

export default function BenefitSection({ benefits }) {
  const defaultBenefits = [
    {
      id: 'b1',
      title: '降低运营和维护成本',
      description:
        'GoodCloud大大降低了企业和公共部门的运营风险，因为它是一个现成的平台，用户不必冒险投入大量资金和时间来自行开发和维护一个平台。',
    },
    {
      id: 'b2',
      title: '为决策者提供一目了然的见解',
      description:
        '所有重要数据都显示在用户友好且视觉上令人愉悦的仪表板中，用户可以一目了然地获得关键见解，并最终帮助用户在更短的时间内做出更好的决策。',
      imageUrl: '/images/goodcloud/dashboard.jpg',
      imageAlt: 'user-friendly and visually-pleasing dashboard',
    },
  ];

  const displayBenefits = benefits || defaultBenefits;

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {displayBenefits.map((benefit, index) => (
          <BenefitItem
            key={benefit.id}
            title={benefit.title}
            description={benefit.description}
            imageUrl={benefit.imageUrl}
            imageAlt={benefit.imageAlt}
            reverse={index % 2 === 0} // 奇数项反转布局
          />
        ))}
      </div>
    </section>
  );
}
