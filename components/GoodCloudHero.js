import Link from 'next/link';

export default function GoodCloudHero({ imageUrl }) {
  return (
    <section
      className="relative w-full h-[500px] md:h-auto md:min-h-[400px] bg-cover bg-center flex items-center justify-center text-center text-white"
      style={
        imageUrl
          ? { backgroundImage: `url(${imageUrl})` }
          : { backgroundColor: '#f0f2f5' }
      }
    >
      <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      <div className="relative z-10 max-w-7xl px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8">
        <h1 className="text-4xl md:text-[42px] font-bold mb-5">
          GoodCloud物联网设备管理系统
        </h1>
        <p className="text-lg md:text-[22px] mb-8 max-w-3xl mx-auto">
          物联网项目的设备管理、数据收集、处理和可视化
        </p>
        <div className="mt-8">
          <Link
            href="/experience"
            className="inline-block bg-white text-[#17a2b8] py-4 px-8 rounded-full text-[15px] font-medium no-underline border border-gray-300 transition-all hover:bg-gray-200 hover:border-gray-400"
          >
            平台体验
          </Link>
        </div>
      </div>
    </section>
  );
}
