export default function Pagination({ currentPage, totalPages, onPageChange }) {
  if (totalPages <= 1) {
    return null; // 如果只有一页或没有页，则不显示分页
  }

  const pageNumbers = [];
  // 简单分页逻辑：显示所有页码
  // 更复杂的逻辑可以添加省略号，例如： [1, '...', 5, 6, 7, '...', 10]
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  const baseClasses =
    'flex items-center justify-center min-w-[40px] h-10 px-4 py-2 no-underline border cursor-pointer transition-all duration-300 text-sm font-medium rounded-md shadow-sm';
  const defaultClasses =
    'text-gray-500 border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-700 hover:border-gray-400 hover:-translate-y-px hover:shadow-md';
  const activeClasses =
    'bg-blue-500 text-white border-blue-500 cursor-default shadow-lg hover:bg-blue-600 hover:border-blue-600';
  const disabledClasses =
    'text-gray-400 cursor-not-allowed bg-gray-100 border-gray-300 shadow-none hover:bg-gray-100 hover:text-gray-400 hover:border-gray-300 hover:transform-none';

  return (
    <nav
      aria-label="Page navigation"
      className="flex justify-center mt-10 py-6"
    >
      <ul className="list-none p-0 m-0 flex items-center gap-2">
        <li>
          <button
            className={`${baseClasses} ${currentPage === 1 ? disabledClasses : defaultClasses}`}
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Previous Page"
          >
            上一页
          </button>
        </li>
        {pageNumbers.map(number => (
          <li key={number}>
            <button
              className={`${baseClasses} ${currentPage === number ? activeClasses : defaultClasses}`}
              onClick={() => onPageChange(number)}
            >
              {number}
            </button>
          </li>
        ))}
        <li>
          <button
            className={`${baseClasses} ${currentPage === totalPages ? disabledClasses : defaultClasses}`}
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            aria-label="Next Page"
          >
            下一页
          </button>
        </li>
      </ul>
    </nav>
  );
}
