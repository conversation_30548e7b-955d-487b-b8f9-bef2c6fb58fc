const alignmentClasses = {
  center: 'text-center',
  left: 'text-left',
  right: 'text-right',
};

const subtitleAlignmentClasses = {
  center: 'mx-auto',
  left: 'mr-auto', // or just '' if you want it to fill available space
  right: 'ml-auto',
};

export default function SectionTitle({
  title,
  subtitle,
  alignment = 'center',
}) {
  const alignClass = alignmentClasses[alignment] || alignmentClasses.center;
  const subtitleAlignClass =
    subtitleAlignmentClasses[alignment] || subtitleAlignmentClasses.center;

  return (
    <div className={`mb-10 ${alignClass}`}>
      {title && (
        <h2 className="text-3xl font-bold text-gray-800 mb-2.5 leading-tight">
          {title}
        </h2>
      )}
      {subtitle && (
        <p
          className={`text-lg text-gray-600 max-w-2xl leading-relaxed ${subtitleAlignClass}`}
        >
          {subtitle}
        </p>
      )}
    </div>
  );
}
