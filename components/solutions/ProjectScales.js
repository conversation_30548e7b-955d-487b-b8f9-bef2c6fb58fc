const scales = [
  {
    id: 1,
    title: '初创公司',
    description: '用于市场验证和用户获取的小批量生产。',
    imageUrl: '/images/solutions/white-label/scale-startup.png',
  },
  {
    id: 2,
    title: '中小企业',
    description: '经济高效的部署和自定义应用程序的选择性功能。',
    imageUrl: '/images/solutions/white-label/scale-sme.png',
  },
  {
    id: 3,
    title: '大型企业',
    description: '可高效定制的硬件和软件。',
    imageUrl: '/images/solutions/white-label/scale-enterprise.png',
  },
];

export default function ProjectScales() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl font-extrabold mb-12">
          高度适配不同规模的项目
        </h2>
        <div className="flex flex-col sm:flex-col md:flex-row justify-center items-stretch gap-8">
          {scales.map(scale => (
            <div
              key={scale.id}
              className="w-full sm:w-full sm:w-full md:w-1/3 md:max-w-xs p-8 bg-white rounded-lg shadow-md transition-shadow flex flex-col items-center"
            >
              <div className="h-32 mb-6 flex items-center justify-center">
                <img
                  src={scale.imageUrl}
                  alt={scale.title}
                  className="max-w-full max-h-full object-contain p-2"
                />
              </div>
              <h3 className="text-2xl font-semibold mb-3">{scale.title}</h3>
              <p className="text-gray-600">{scale.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
