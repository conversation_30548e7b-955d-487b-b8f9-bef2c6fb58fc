import Link from 'next/link';

export default function StartProjectCTA({ imageUrl }) {
  return (
    <section
      className="relative bg-cover bg-center text-white py-20"
      style={{ backgroundImage: `url(${imageUrl})` }}
    >
      <div className="absolute inset-0 bg-black opacity-60"></div>
      <div className="relative w-full max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-col sm:flex-col sm:flex-col sm:flex-col md:flex-row justify-center items-center md:gap-x-16">
          <div className="text-center sm:text-center sm:text-center sm:text-center md:text-left mb-8 md:mb-0">
            <h2 className="text-4xl font-extrabold mb-2">立即开始您的项目</h2>
            <p className="text-lg text-gray-200">
              与我们的专家交谈以获得定制的解决方案....
            </p>
          </div>
          <div>
            <Link
              href="/contact"
              className="inline-block bg-white text-black py-4 px-8 rounded-full font-bold text-base transition hover:bg-gray-200"
            >
              联系销售
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
