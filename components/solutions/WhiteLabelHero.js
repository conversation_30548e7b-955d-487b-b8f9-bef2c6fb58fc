import Link from 'next/link';

export default function WhiteLabelHero({ imageUrl }) {
  return (
    <div
      className="relative bg-cover bg-center text-white py-12 md:py-20"
      style={{ backgroundImage: `url(${imageUrl})` }}
    >
      <div className="absolute inset-0 bg-black opacity-50"></div>
      <div className="relative w-full max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8 text-center flex flex-col items-center">
        <h1 className="text-3xl md:text-5xl font-black leading-tight mb-8">
          白标及客制化服务
        </h1>
        <Link
          href="/contact"
          className="inline-block bg-white text-black py-4 px-8 rounded-full font-bold text-lg transition hover:bg-gray-200"
        >
          联系销售
        </Link>
      </div>
    </div>
  );
}
