import { useState } from 'react';

const steps = [
  {
    id: 1,
    title: '场景和目标预设',
    description:
      '通过提供技术和定制培训来协助可扩展部署，并为服务维护和进一步开发提供持续支持。',
    imageUrl: '/images/solutions/white-label/how-it-works-1.png',
  },
  {
    id: 2,
    title: '严格的设计指标',
    description:
      '我们根据要求的规格开发概念或样品设计，并与客户一起进行进一步的调整和优化。',
    imageUrl: '/images/solutions/white-label/how-it-works-2.png',
  },
  {
    id: 3,
    title: '规范的产品测试',
    description:
      '我们为功能压力测试创建原型，评估用户体验以及与客户现有服务组合的兼容性。',
    imageUrl: '/images/solutions/white-label/how-it-works-3.png',
  },
  {
    id: 4,
    title: '大规模制造能力',
    description:
      '通过提供技术和定制培训来协助可扩展部署，并为服务维护和进一步开发提供持续支持。',
    imageUrl: '/images/solutions/white-label/how-it-works-4.png',
  },
];

export default function HowItWorks() {
  const [activeStep, setActiveStep] = useState(0);

  return (
    <section className="py-20 bg-white">
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8">
        <h2 className="text-4xl font-extrabold mb-12 text-center">
          How it Works
        </h2>

        <div className="relative min-h-[300px]">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`transition-opacity duration-500 ease-in-out ${activeStep === index ? 'opacity-100' : 'opacity-0 absolute top-0 left-0 w-full'}`}
            >
              {activeStep === index && (
                <div className="grid sm:grid-cols-1 sm:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-center">
                  <div className="text-left">
                    <span className="text-gray-500 font-semibold">
                      步骤 {step.id}
                    </span>
                    <h3 className="text-3xl font-bold my-4">{step.title}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                  <div>
                    <img
                      src={step.imageUrl}
                      alt={step.title}
                      className="w-full h-auto object-contain rounded-lg"
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-12">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveStep(index)}
              className={`w-7 h-1 mx-1 transition-colors duration-300 ${activeStep === index ? 'bg-gray-600' : 'bg-gray-300 hover:bg-gray-400'}`}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
