const reasons = [
  {
    id: 1,
    mainText: '10 年+',
    subText: '网络解决方案经验',
    imageUrl: '/images/solutions/white-label/why-partner-experience.jpg',
  },
  {
    id: 2,
    mainText: '强大',
    subText: 'IT 和工程支持',
    imageUrl: '/images/solutions/white-label/why-partner-support.jpg',
  },
  {
    id: 3,
    mainText: '庞大',
    subText: '个人和企业客户群',
    imageUrl: '/images/solutions/white-label/why-partner-customers.jpg',
  },
  {
    id: 4,
    mainText: '广泛的',
    subText: '质量保证',
    imageUrl: '/images/solutions/white-label/why-partner-quality.jpg',
  },
];

export default function WhyPartner() {
  return (
    <section className="py-20 bg-white">
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-extrabold">为什么与 GL.iNet 合作？</h2>
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            GL.iNet 是 Wi-Fi 和物联网应用网络安全基础设施的方案解决者。
            我们的工程师团队热衷于了解您的挑战，利用他们的专业知识定制网络解决方案以满足您的需求并解决您的问题。我们相信一切都会成功
            企业建立在强大而安全的基础之上。
          </p>
        </div>
        <div className="grid md:grid-cols-4 gap-8">
          {reasons.map(reason => (
            <div
              key={reason.id}
              className="relative rounded-lg overflow-hidden h-48 text-white flex flex-col justify-center items-center text-center p-4 transition-transform duration-300 ease-in-out hover:scale-105"
            >
              <img
                src={reason.imageUrl}
                alt={reason.subText}
                className="absolute inset-0 w-full h-full object-cover z-0"
              />
              <div className="absolute inset-0 bg-black opacity-50 z-10"></div>
              <div className="relative z-20">
                <h3 className="text-2xl font-bold">{reason.mainText}</h3>
                <p className="mt-1">{reason.subText}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
