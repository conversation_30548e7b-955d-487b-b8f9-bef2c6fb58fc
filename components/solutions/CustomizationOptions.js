const options = [
  {
    id: 1,
    name: '表壳丝印',
    imageUrl: '/images/solutions/white-label/custom-casing.png',
  },
  {
    id: 2,
    name: '产品贴纸',
    imageUrl: '/images/solutions/white-label/custom-sticker.png',
  },
  {
    id: 3,
    name: '包装设计',
    imageUrl: '/images/solutions/white-label/custom-packaging.png',
  },
  {
    id: 4,
    name: '用户手册',
    imageUrl: '/images/solutions/white-label/custom-manual.png',
  },
];

export default function CustomizationOptions() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 sm:px-6 sm:px-6 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl font-extrabold mb-12">路由器/网关白标定制</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {options.map(option => (
            <div
              key={option.id}
              className="flex flex-col items-center justify-start"
            >
              <div className="h-40 w-full flex items-center justify-center mb-4">
                <img
                  src={option.imageUrl}
                  alt={option.name}
                  className="max-h-full max-w-full object-contain"
                />
              </div>
              <h3 className="text-lg font-semibold">{option.name}</h3>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
