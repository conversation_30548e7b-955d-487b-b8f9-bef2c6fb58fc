const platforms = [
  {
    id: 1,
    title: 'GoodCloud 远程设备管理平台',
    description:
      'GL.iNet的所有路由器都带有GoodCloud远程设备管理平台，用于远程访问设备终端， 网络分析和批量固件升级。 我们的平台也是高度可定制的，包括我们客户的首选 界面、分析和徽标。',
    imageUrl: '/images/solutions/white-label/platform-goodcloud.jpg',
  },
  {
    id: 2,
    title: 'OpenWrt 操作系统固件',
    description:
      'GL.iNet 的硬件运行在高性能和安全的开源 OpenWrt 操作系统上，它带有我们用户友好的 Imagebuilder 用于轻松定制品牌徽标和固件包的应用程序。',
    imageUrl: '/images/solutions/white-label/platform-openwrt.png',
  },
];

export default function BusinessPlatform() {
  return (
    <section className="py-20 bg-white">
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-extrabold">
            与您定制目标一致的业务平台
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            设计您自己的固件 OpenWrt, Image Builder, SDK 和 API
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-12 items-start">
          {platforms.map(platform => (
            <div key={platform.id} className="text-left">
              <div className="mb-6 h-96 flex items-center justify-center">
                <img
                  src={platform.imageUrl}
                  alt={platform.title}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
              <h3 className="text-2xl font-semibold mb-4">{platform.title}</h3>
              <p className="text-gray-600 leading-relaxed">
                {platform.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
