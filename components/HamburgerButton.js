export default function HamburgerButton({ isOpen, onClick, className = '' }) {
  return (
    <button
      onClick={onClick}
      className={`relative w-8 h-8 flex flex-col justify-center items-center space-y-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded transition-all duration-300 ${className}`}
      aria-label={isOpen ? '关闭菜单' : '打开菜单'}
      aria-expanded={isOpen}
    >
      {/* 第一条线 */}
      <span
        className={`block w-6 h-0.5 bg-gray-600 transition-all duration-300 transform ${
          isOpen ? 'rotate-45 translate-y-1.5' : ''
        }`}
      />
      {/* 第二条线 */}
      <span
        className={`block w-6 h-0.5 bg-gray-600 transition-all duration-300 ${
          isOpen ? 'opacity-0' : 'opacity-100'
        }`}
      />
      {/* 第三条线 */}
      <span
        className={`block w-6 h-0.5 bg-gray-600 transition-all duration-300 transform ${
          isOpen ? '-rotate-45 -translate-y-1.5' : ''
        }`}
      />
    </button>
  );
}
