import Image from 'next/image';
import Link from 'next/link';

export default function ProductGridCard({ product }) {
  // 使用占位符以防关键数据缺失
  const productName = product?.name || '产品名称';
  const productDescription = product?.description || '产品描述信息。';
  const productImageUrl =
    product?.imageUrl || '/images/placeholder-product.png'; // 通用产品占位图
  const productLink = product?.link || '#';

  return (
    <Link
      href={productLink}
      className="block group rounded-lg overflow-hidden no-underline text-current bg-white transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
    >
      <div className="w-full pt-[55%] relative flex items-center justify-center overflow-hidden bg-white">
        <Image
          src={productImageUrl}
          alt={productName}
          layout="fill"
          objectFit="contain"
          className="p-2.5 transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      <div className="p-2.5 text-center transition-colors duration-300 group-hover:bg-blue-500">
        <h3 className="text-sm font-semibold mb-1.5 text-gray-800 line-clamp-2 transition-colors duration-300 group-hover:text-white">
          {productName}
        </h3>
        {productDescription && (
          <p className="text-xs text-gray-500 line-clamp-2 transition-colors duration-300 group-hover:text-white">
            {productDescription}
          </p>
        )}
      </div>
    </Link>
  );
}
