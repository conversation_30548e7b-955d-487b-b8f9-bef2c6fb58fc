import { useState, useEffect, useCallback } from 'react';
import HeroSlide from './HeroSlide';

// 轮播数据 - 图片使用占位符
const slidesData = [
  {
    id: 's200',
    imageUrl: '/images/slide-s200.jpg',
    title: 'GL-S200',
    subtitle: 'Thread 边界路由器',
    buttonText: '了解更多',
    buttonLink: '/products/s200',
    theme: {
      contentTextColorClass: 'textBlack',
      buttonThemeClass: 'buttonThemeBlack',
    },
  },
  {
    id: 'be3600',
    imageUrl: '/images/slide-be3600.jpg',
    title: 'GL-BE3600',
    subtitle: 'Wi-Fi 7便携式路由器',
    buttonText: '立即购买',
    buttonLink: '/products/be3600',
    theme: {
      contentTextColorClass: 'textWhite',
      buttonThemeClass: 'buttonThemeWhite',
    },
  },
  {
    id: 'rm1',
    imageUrl: '/images/slide-rm1.jpg',
    title: 'GL-RM1',
    subtitle: '远程控制设备',
    buttonText: '立即购买',
    buttonLink: '/products/rm1',
    theme: {
      contentTextColorClass: 'textBlack',
      buttonThemeClass: 'buttonThemeBlack',
    },
  },
  {
    id: 'mt6000',
    imageUrl: '/images/slide-mt6000.jpg',
    title: 'GL-MT6000',
    subtitle: 'Wi-Fi 6 AX6000 高性能家用路由器',
    buttonText: '立即购买',
    buttonLink: '/products/mt6000',
    theme: {
      contentTextColorClass: 'textWhite',
      buttonThemeClass: 'buttonThemeBlack',
    },
  },
  {
    id: 'mt3000',
    imageUrl: '/images/slide-mt3000.jpg',
    title: 'GL-MT3000',
    subtitle: '适用于差旅和家庭使用的便携式WiFi 6路由器',
    buttonText: '立即购买',
    buttonLink: '/products/mt3000',
    theme: {
      contentTextColorClass: 'textWhite',
      buttonThemeClass: 'buttonThemeBlue',
    },
  },
];

export default function Carousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? slidesData.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = useCallback(() => {
    const isLastSlide = currentIndex === slidesData.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  }, [currentIndex]);

  const goToSlide = slideIndex => {
    setCurrentIndex(slideIndex);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      goToNext();
    }, 5000); // 每5秒切换一次
    return () => clearTimeout(timer);
  }, [currentIndex, goToNext]);

  if (!slidesData || slidesData.length === 0) {
    return <div>加载轮播图中...</div>; // 或者其他占位符
  }

  return (
    <div className="relative w-full overflow-hidden bg-gray-100">
      <div
        className="flex transition-transform duration-500 ease-in-out w-full"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {slidesData.map(slide => (
          <HeroSlide key={slide.id} slide={slide} />
        ))}
      </div>

      <button
        className="absolute top-1/2 -translate-y-1/2 left-5 text-white cursor-pointer z-10 transition-opacity duration-300 opacity-50 hover:opacity-100"
        onClick={goToPrevious}
        aria-label="Previous Slide"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-12 h-12"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M15.75 19.5L8.25 12l7.5-7.5"
          />
        </svg>
      </button>
      <button
        className="absolute top-1/2 -translate-y-1/2 right-5 text-white cursor-pointer z-10 transition-opacity duration-300 opacity-50 hover:opacity-100"
        onClick={goToNext}
        aria-label="Next Slide"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-12 h-12"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M8.25 4.5l7.5 7.5-7.5 7.5"
          />
        </svg>
      </button>

      <div className="absolute bottom-5 left-1/2 -translate-x-1/2 flex z-10">
        {slidesData.map((slide, slideIndex) => (
          <button
            key={slide.id}
            className={`w-8 h-1 mx-1.5 cursor-pointer transition-colors duration-300 ${currentIndex === slideIndex ? 'bg-white' : 'bg-white bg-opacity-50 hover:bg-opacity-80'}`}
            onClick={() => goToSlide(slideIndex)}
            aria-label={`Go to slide ${slideIndex + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
