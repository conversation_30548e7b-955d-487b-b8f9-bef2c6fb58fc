// 产品相关类型定义
export interface Product {
  id: string;
  name: string;
  imageUrl: string;
  imageUrlHover?: string;
  description: string;
  link: string;
}

// 解决方案相关类型定义
export interface Solution {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}

// 导航相关类型定义
export interface NavItem {
  label: string;
  href: string;
  children?: NavItem[];
}

// 媒体合作伙伴类型定义
export interface MediaPartner {
  id: string;
  name: string;
  logoUrl: string;
  linkUrl: string;
}

// 轮播图相关类型定义
export interface CarouselSlide {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl: string;
  link?: string;
}

// 特性卡片类型定义
export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon?: string;
  imageUrl?: string;
}

// 分页相关类型定义
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

// 布局相关类型定义
export interface LayoutProps {
  children: React.ReactNode;
  title?: string;
}

// 按钮相关类型定义
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  href?: string;
  className?: string;
  disabled?: boolean;
}

// 模态框相关类型定义
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}
