# 响应式断点使用指南

## 📱 设备尺寸定义

| 断点 | 尺寸 | 设备类型 | 使用场景 |
|------|------|----------|----------|
| `xs:` | 475px+ | 大手机 | 文字大小调整、小间距优化 |
| `sm:` | 640px+ | 小平板/大手机横屏 | 布局开始变化、按钮排列 |
| `md:` | 768px+ | 平板 | 主要布局切换点、网格变化 |
| `lg:` | 1024px+ | 小桌面 | 桌面布局、多列显示 |
| `xl:` | 1280px+ | 大桌面 | 最大内容宽度、精细调整 |
| `2xl:` | 1536px+ | 超大桌面 | 超宽屏优化 |

## 🎯 标准化使用原则

### **1. 移动优先原则**
```css
/* ✅ 正确：从小屏开始，逐步增强 */
className="text-sm sm:text-base md:text-lg lg:text-xl"

/* ❌ 错误：跳跃式断点 */
className="text-sm lg:text-xl"
```

### **2. 布局断点策略**
```css
/* 网格布局标准模式 */
grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4

/* 弹性布局标准模式 */
flex-col sm:flex-row

/* 间距标准模式 */
px-4 sm:px-6 lg:px-8
py-8 sm:py-12 lg:py-16
```

### **3. 文字大小标准**
```css
/* 标题层级 */
h1: text-2xl sm:text-3xl md:text-4xl lg:text-5xl
h2: text-xl sm:text-2xl md:text-3xl lg:text-4xl  
h3: text-lg sm:text-xl md:text-2xl
h4: text-base sm:text-lg md:text-xl

/* 正文文字 */
正文: text-sm sm:text-base
小字: text-xs sm:text-sm
```

## 📋 常用模式库

### **容器宽度**
```css
/* 标准容器 */
max-w-7xl mx-auto px-4 sm:px-6 lg:px-8

/* 内容容器 */
max-w-4xl mx-auto px-4 sm:px-6

/* 窄容器 */
max-w-2xl mx-auto px-4
```

### **网格布局**
```css
/* 产品网格 */
grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8

/* 功能网格 */
grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8

/* 卡片网格 */
grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6
```

### **按钮组**
```css
/* 垂直到水平 */
flex flex-col sm:flex-row gap-2 sm:gap-4

/* 按钮尺寸 */
w-full sm:w-auto
```

## 🚫 避免的反模式

### **1. 跳跃断点**
```css
/* ❌ 避免：跳过中间断点 */
text-sm lg:text-xl

/* ✅ 推荐：渐进式增强 */
text-sm sm:text-base md:text-lg lg:text-xl
```

### **2. 不一致的间距**
```css
/* ❌ 避免：随意间距 */
px-3 md:px-7 xl:px-12

/* ✅ 推荐：标准间距 */
px-4 sm:px-6 lg:px-8
```

### **3. 过度复杂的断点**
```css
/* ❌ 避免：过多断点 */
text-xs xs:text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl

/* ✅ 推荐：关键断点 */
text-sm sm:text-base lg:text-lg
```

## 🔧 实施检查清单

- [ ] 所有组件使用移动优先原则
- [ ] 断点使用连续，不跳跃
- [ ] 间距使用标准化数值
- [ ] 文字大小遵循层级标准
- [ ] 容器宽度使用预定义类
- [ ] 网格布局使用标准模式
