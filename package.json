{"name": "gl-inet-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky", "analyze": "ANALYZE=true npm run build", "check-breakpoints": "node scripts/check-breakpoints.js", "check-all": "npm run lint && npm run type-check && npm run check-breakpoints"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.4", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.19", "eslint": "^9.32.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}