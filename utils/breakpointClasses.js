/**
 * 标准化断点工具类
 * 提供一致的响应式类名组合
 */

// 容器类
export const containers = {
  // 标准容器
  standard: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  // 内容容器
  content: 'max-w-4xl mx-auto px-4 sm:px-6',
  // 窄容器
  narrow: 'max-w-2xl mx-auto px-4',
  // 宽容器
  wide: 'max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8',
};

// 网格布局类
export const grids = {
  // 产品网格 (1-2-3-4列)
  products: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8',
  // 功能网格 (1-2列)
  features: 'grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8',
  // 卡片网格 (1-2-3列)
  cards: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6',
  // 解决方案网格 (1-2-4列)
  solutions: 'grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-8',
  // 简单两列
  twoCol: 'grid grid-cols-1 md:grid-cols-2 gap-6',
};

// 弹性布局类
export const flexLayouts = {
  // 垂直到水平
  colToRow: 'flex flex-col sm:flex-row',
  // 居中对齐
  center: 'flex items-center justify-center',
  // 两端对齐
  between: 'flex items-center justify-between',
  // 垂直居中
  centerY: 'flex items-center',
  // 水平居中
  centerX: 'flex justify-center',
  // 按钮组
  buttonGroup: 'flex flex-col sm:flex-row gap-2 sm:gap-4',
};

// 文字大小类
export const typography = {
  // 标题层级
  h1: 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold',
  h2: 'text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold',
  h3: 'text-lg sm:text-xl md:text-2xl font-bold',
  h4: 'text-base sm:text-lg md:text-xl font-bold',
  h5: 'text-sm sm:text-base md:text-lg font-bold',
  
  // 正文文字
  body: 'text-sm sm:text-base',
  bodyLarge: 'text-base sm:text-lg',
  small: 'text-xs sm:text-sm',
  
  // 特殊用途
  hero: 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-extrabold',
  subtitle: 'text-base sm:text-lg md:text-xl',
};

// 间距类
export const spacing = {
  // 内边距
  paddingStandard: 'px-4 sm:px-6 lg:px-8',
  paddingSection: 'py-8 sm:py-12 lg:py-16',
  paddingHero: 'py-12 sm:py-16 lg:py-20',
  
  // 外边距
  marginSection: 'mb-8 sm:mb-12 lg:mb-16',
  marginElement: 'mb-4 sm:mb-6',
  
  // 间隙
  gapSmall: 'gap-2 sm:gap-4',
  gapMedium: 'gap-4 sm:gap-6 lg:gap-8',
  gapLarge: 'gap-6 sm:gap-8 lg:gap-12',
};

// 按钮响应式类
export const buttons = {
  // 全宽到自动宽度
  responsive: 'w-full sm:w-auto',
  // 按钮组间距
  group: 'flex flex-col sm:flex-row gap-2 sm:gap-4',
  // 居中按钮组
  groupCenter: 'flex flex-col sm:flex-row gap-2 sm:gap-4 justify-center',
};

// 图片响应式类
export const images = {
  // 响应式高度
  heroHeight: 'h-64 sm:h-80 md:h-96 lg:h-[500px]',
  cardHeight: 'h-40 sm:h-48 md:h-56',
  iconSize: 'h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12',
  logoSize: 'h-32 w-32 sm:h-40 sm:w-40 md:h-48 md:w-48',
};

// 组合工具函数
export const combineClasses = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

// 获取标准化类名的辅助函数
export const getResponsiveClasses = (type, variant = 'standard') => {
  const classMap = {
    container: containers,
    grid: grids,
    flex: flexLayouts,
    text: typography,
    spacing: spacing,
    button: buttons,
    image: images,
  };
  
  return classMap[type]?.[variant] || '';
};

// 使用示例：
// import { containers, grids, typography } from '@/utils/breakpointClasses';
// 
// <div className={containers.standard}>
//   <h1 className={typography.h1}>标题</h1>
//   <div className={grids.products}>
//     {/* 产品列表 */}
//   </div>
// </div>
