// 生成组织结构化数据
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'GL.iNet',
    url: 'https://www.gl-inet.com',
    logo: 'https://www.gl-inet.com/images/logo.png',
    description: 'GL.iNet 提供领先的路由器和网络解决方案，专注于物联网网关、企业网络和智能家居连接。',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+86-755-8629-6286',
      contactType: 'customer service',
      availableLanguage: ['Chinese', 'English'],
    },
    sameAs: [
      'https://www.facebook.com/gl.inet.wifi',
      'https://twitter.com/GLiNetWiFi',
      'https://www.youtube.com/channel/UCBfNEzurltlIeFFCbUgHQyg',
    ],
  };
}

// 生成产品结构化数据
export function generateProductSchema(product) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.imageUrl,
    brand: {
      '@type': 'Brand',
      name: 'GL.iNet',
    },
    manufacturer: {
      '@type': 'Organization',
      name: 'GL.iNet',
    },
    category: product.category || '网络设备',
    offers: {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      priceCurrency: 'CNY',
      seller: {
        '@type': 'Organization',
        name: 'GL.iNet',
      },
    },
  };
}

// 生成文章结构化数据
export function generateArticleSchema(article) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    image: article.imageUrl,
    author: {
      '@type': 'Organization',
      name: 'GL.iNet',
    },
    publisher: {
      '@type': 'Organization',
      name: 'GL.iNet',
      logo: {
        '@type': 'ImageObject',
        url: 'https://www.gl-inet.com/images/logo.png',
      },
    },
    datePublished: article.publishDate,
    dateModified: article.modifiedDate || article.publishDate,
  };
}

// 生成面包屑结构化数据
export function generateBreadcrumbSchema(breadcrumbs) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// 生成FAQ结构化数据
export function generateFAQSchema(faqs) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}
