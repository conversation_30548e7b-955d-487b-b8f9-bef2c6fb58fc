// sl-inet/data/mediaPartners.js
export const mediaPartnersData = [
  {
    id: 'partner1',
    name: '合作伙伴 1',
    logoUrl: '/images/solutions/partner1.png',
    linkUrl: '#',
  },
  {
    id: 'partner2',
    name: '合作伙伴 2',
    logoUrl: '/images/solutions/partner2.png',
    linkUrl: '#',
  },
  {
    id: 'partner3',
    name: '合作伙伴 3',
    logoUrl: '/images/solutions/partner3.png',
    linkUrl: '#',
  },
  {
    id: 'partner4',
    name: '合作伙伴 4',
    logoUrl: '/images/solutions/partner4.png',
    linkUrl: '#',
  },
  {
    id: 'partner5',
    name: '合作伙伴 5',
    logoUrl: '/images/solutions/partner5.png',
    linkUrl: '#',
  },
  {
    id: 'partner6',
    name: '合作伙伴 6',
    logoUrl: '/images/solutions/partner6.png',
    linkUrl: '#',
  },
  {
    id: 'partner7',
    name: '合作伙伴 7',
    logoUrl: '/images/solutions/partner7.png',
    linkUrl: '#',
  },
  {
    id: 'partner8',
    name: '合作伙伴 8',
    logoUrl: '/images/solutions/partner8.png',
    linkUrl: '#',
  },
  {
    id: 'partner9',
    name: '合作伙伴 9',
    logoUrl: '/images/solutions/partner9.png',
    linkUrl: '#',
  },
  {
    id: 'partner10',
    name: '合作伙伴 10',
    logoUrl: '/images/solutions/partner10.png',
    linkUrl: '#',
  },
  {
    id: 'partner11',
    name: '合作伙伴 11',
    logoUrl: '/images/solutions/partner11.png',
    linkUrl: '#',
  },
  {
    id: 'partner12',
    name: '合作伙伴 12',
    logoUrl: '/images/solutions/partner12.png',
    linkUrl: '#',
  },
  {
    id: 'partner13',
    name: '合作伙伴 13',
    logoUrl: '/images/solutions/partner13.png',
    linkUrl: '#',
  },
];

// 媒体轮播数据 - 13个媒体伙伴
export const mediaCarouselData = [
  {
    id: 'media1',
    name: 'TechCrunch',
    logoUrl: '/images/media/media-1.png',
  },
  {
    id: 'media2',
    name: 'Gadgeteer',
    logoUrl: '/images/media/media-2.png',
  },
  {
    id: 'media3',
    name: '企鹅号',
    logoUrl: '/images/media/media-3.png',
  },
  {
    id: 'media4',
    name: 'ADSL Zone',
    logoUrl: '/images/media/media-4.png',
  },
  {
    id: 'media5',
    name: 'Linux Journal',
    logoUrl: '/images/media/media-5.png',
  },
  {
    id: 'media6',
    name: 'OpenWrt',
    logoUrl: '/images/media/media-6.png',
  },
  {
    id: 'media7',
    name: 'IoT World',
    logoUrl: '/images/media/media-7.png',
  },
  {
    id: 'media8',
    name: 'Network World',
    logoUrl: '/images/media/media-8.png',
  },
  {
    id: 'media9',
    name: 'Tech Republic',
    logoUrl: '/images/media/media-9.png',
  },
  {
    id: 'media10',
    name: 'Router Guide',
    logoUrl: '/images/media/media-10.png',
  },
  {
    id: 'media11',
    name: 'IT Pro',
    logoUrl: '/images/media/media-11.png',
  },
  {
    id: 'media12',
    name: 'Security Today',
    logoUrl: '/images/media/media-12.png',
  },
  {
    id: 'media13',
    name: 'Wireless Week',
    logoUrl: '/images/media/media-13.png',
  },
];
